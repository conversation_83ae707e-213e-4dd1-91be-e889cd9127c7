import { get, post, put, del } from '@/utils/request'
import type {
  ApiResponse,
  GuidelineInfo,
  GuidelineInfoSearchParams,
  GuidelineFormData,
  GuidelineApprovalData,
  GuidelineDeleteData,
  GuidelineInfoListResponse,
  DictData,
} from './types'

// ==================== 核算系数库相关API ====================
export const guidelineApi = {
  // 1. 获取核算系数库列表
  getGuidelineInfoList: (params: GuidelineInfoSearchParams): Promise<ApiResponse<GuidelineInfoListResponse>> => {
    return post('/GuidelineInfo/GuidelineInfoList', params)
  },

  // 2. 新增核算系数库
  saveGuidelineInfo: (data: GuidelineFormData): Promise<ApiResponse> => {
    return post('/GuidelineInfo/saveGuidelineInfo', data)
  },

  // 3. 编辑核算系数库
  updateGuidelineInfo: (data: GuidelineFormData): Promise<ApiResponse> => {
    return post('/GuidelineInfo/updateGuidelineInfo', data)
  },

  // 4. 删除核算系数库
  deleteGuidelineInfo: (data: GuidelineDeleteData): Promise<ApiResponse> => {
    return post('/GuidelineInfo/deleteGuidelineInfo', data)
  },

  // 5. 获取状态下拉
  getDataStatusList: (): Promise<ApiResponse<DictData[]>> => {
    return get('/GuidelineInfo/getDataStatusList')
  },

  // 6. 审核接口
  approvedGuidelineInfo: (data: GuidelineApprovalData): Promise<ApiResponse> => {
    return post('/GuidelineInfo/approvedGuidelineInfo', data)
  },
}
