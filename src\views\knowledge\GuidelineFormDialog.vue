<template>
  <el-dialog
    v-model="dialogVisible"
    :title="isEdit ? '编辑核算系数库' : '新增核算系数库'"
    width="800px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="120px"
      label-position="left"
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="核算指南名称" prop="guidelineName">
            <el-input
              v-model="form.guidelineName"
              placeholder="请输入核算指南名称"
              maxlength="100"
              show-word-limit
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="发布部门" prop="issuingDepartment">
            <el-input
              v-model="form.issuingDepartment"
              placeholder="请输入发布部门"
              maxlength="100"
              show-word-limit
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="发布日期" prop="issueDate">
            <el-date-picker
              v-model="form.issueDate"
              type="date"
              placeholder="请选择发布日期"
              style="width: 100%"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="用途" prop="purpose">
            <el-select
              v-model="form.purpose"
              placeholder="请选择用途"
              multiple
              style="width: 100%"
            >
              <el-option
                v-for="item in purposeOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-form-item label="适用范围" prop="applicableScope">
        <el-input
          v-model="form.applicableScope"
          type="textarea"
          :rows="3"
          placeholder="请输入适用范围"
          maxlength="500"
          show-word-limit
        />
      </el-form-item>

      <el-form-item label="核算对象说明" prop="accountingEntity">
        <el-input
          v-model="form.accountingEntity"
          type="textarea"
          :rows="3"
          placeholder="请输入核算对象说明"
          maxlength="500"
          show-word-limit
        />
      </el-form-item>

      <el-form-item label="核算方法" prop="methodology">
        <el-input
          v-model="form.methodology"
          type="textarea"
          :rows="3"
          placeholder="请输入核算方法"
          maxlength="1000"
          show-word-limit
        />
      </el-form-item>

      <el-form-item label="关键公式" prop="keyFormulas">
        <el-input
          v-model="form.keyFormulas"
          type="textarea"
          :rows="3"
          placeholder="请输入关键公式"
          maxlength="1000"
          show-word-limit
        />
      </el-form-item>

      <el-form-item label="备注" prop="remarks">
        <el-input
          v-model="form.remarks"
          type="textarea"
          :rows="3"
          placeholder="请输入备注"
          maxlength="500"
          show-word-limit
        />
      </el-form-item>

      <el-form-item label="附件上传">
        <el-upload
          ref="uploadRef"
          :file-list="fileList"
          :auto-upload="false"
          :on-change="handleFileChange"
          :on-remove="handleFileRemove"
          multiple
          drag
        >
          <el-icon class="el-icon--upload"><upload-filled /></el-icon>
          <div class="el-upload__text">
            将文件拖到此处，或<em>点击上传</em>
          </div>
          <template #tip>
            <div class="el-upload__tip">
              支持 PDF、DOC、DOCX、XLS、XLSX 格式，单个文件不超过 10MB
            </div>
          </template>
        </el-upload>
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button @click="handleSaveDraft" :loading="saving">保存草稿</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="submitting">
          提交
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, watch, computed } from 'vue'
import { ElMessage, type FormInstance, type FormRules } from 'element-plus'
import { UploadFilled } from '@element-plus/icons-vue'
import type { GuidelineInfo, GuidelineFormData } from '@/api/types'
import { guidelineApi } from '@/api/guideline'
import { fetchDictData } from '@/utils/options'

// Props
interface Props {
  visible: boolean
  formData?: GuidelineFormData | null
  isEdit?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  formData: null,
  isEdit: false,
})

// Emits
const emit = defineEmits<{
  'update:visible': [value: boolean]
  success: []
}>()

// 响应式数据
const formRef = ref<FormInstance>()
const uploadRef = ref()
const saving = ref(false)
const submitting = ref(false)
const fileList = ref<any[]>([])
const purposeOptions = ref<any[]>([])

// 计算属性
const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value),
})

// 表单数据
const form = reactive<GuidelineInfo>({
  guidelineId: '',
  guidelineName: '',
  issuingDepartment: '',
  issueDate: '',
  purpose: [],
  applicableScope: '',
  accountingEntity: '',
  methodology: '',
  keyFormulas: '',
  dataStatus: 2, // 默认暂存
  remarks: '',
})

// 表单验证规则
const rules: FormRules = {
  guidelineName: [
    { required: true, message: '请输入核算指南名称', trigger: 'blur' },
    { min: 1, max: 100, message: '长度在 1 到 100 个字符', trigger: 'blur' },
  ],
  issuingDepartment: [
    { required: true, message: '请输入发布部门', trigger: 'blur' },
    { min: 1, max: 100, message: '长度在 1 到 100 个字符', trigger: 'blur' },
  ],
  issueDate: [
    { required: true, message: '请选择发布日期', trigger: 'change' },
  ],
  purpose: [
    { required: true, message: '请选择用途', trigger: 'change' },
  ],
  applicableScope: [
    { required: true, message: '请输入适用范围', trigger: 'blur' },
    { min: 1, max: 500, message: '长度在 1 到 500 个字符', trigger: 'blur' },
  ],
  accountingEntity: [
    { required: true, message: '请输入核算对象说明', trigger: 'blur' },
    { min: 1, max: 500, message: '长度在 1 到 500 个字符', trigger: 'blur' },
  ],
  methodology: [
    { required: true, message: '请输入核算方法', trigger: 'blur' },
    { min: 1, max: 1000, message: '长度在 1 到 1000 个字符', trigger: 'blur' },
  ],
  keyFormulas: [
    { required: true, message: '请输入关键公式', trigger: 'blur' },
    { min: 1, max: 1000, message: '长度在 1 到 1000 个字符', trigger: 'blur' },
  ],
}

// 初始化字典数据
const initDictData = async () => {
  try {
    purposeOptions.value = await fetchDictData('hsyt')
  } catch (error) {
    console.error('获取字典数据失败:', error)
  }
}

// 重置表单
const resetForm = () => {
  Object.assign(form, {
    guidelineId: '',
    guidelineName: '',
    issuingDepartment: '',
    issueDate: '',
    purpose: [],
    applicableScope: '',
    accountingEntity: '',
    methodology: '',
    keyFormulas: '',
    dataStatus: 2,
    remarks: '',
  })
  fileList.value = []
  formRef.value?.clearValidate()
}

// 文件变化处理
const handleFileChange = (file: any, files: any[]) => {
  // 文件大小限制 10MB
  if (file.size > 10 * 1024 * 1024) {
    ElMessage.error('文件大小不能超过 10MB')
    return false
  }
  
  // 文件类型限制
  const allowedTypes = [
    'application/pdf',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'application/vnd.ms-excel',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
  ]
  
  if (!allowedTypes.includes(file.raw.type)) {
    ElMessage.error('只支持 PDF、DOC、DOCX、XLS、XLSX 格式的文件')
    return false
  }
  
  fileList.value = files
}

// 文件移除处理
const handleFileRemove = (file: any, files: any[]) => {
  fileList.value = files
}

// 保存草稿
const handleSaveDraft = async () => {
  if (!formRef.value) return
  
  try {
    await formRef.value.validate()
    saving.value = true
    
    const formData: GuidelineFormData = {
      info: {
        ...form,
        dataStatus: 2, // 草稿状态
      },
      fileList: {
        addFileIds: [], // 这里需要实际的文件上传逻辑
      },
    }
    
    if (props.isEdit && form.guidelineId) {
      await guidelineApi.updateGuidelineInfo(formData)
      ElMessage.success('保存草稿成功')
    } else {
      await guidelineApi.saveGuidelineInfo(formData)
      ElMessage.success('保存草稿成功')
    }
    
    emit('success')
  } catch (error) {
    console.error('保存草稿失败:', error)
    ElMessage.error('保存草稿失败')
  } finally {
    saving.value = false
  }
}

// 提交
const handleSubmit = async () => {
  if (!formRef.value) return
  
  try {
    await formRef.value.validate()
    submitting.value = true
    
    const formData: GuidelineFormData = {
      info: {
        ...form,
        dataStatus: 3, // 待审核状态
      },
      fileList: {
        addFileIds: [], // 这里需要实际的文件上传逻辑
      },
    }
    
    if (props.isEdit && form.guidelineId) {
      await guidelineApi.updateGuidelineInfo(formData)
      ElMessage.success('提交成功')
    } else {
      await guidelineApi.saveGuidelineInfo(formData)
      ElMessage.success('提交成功')
    }
    
    emit('success')
  } catch (error) {
    console.error('提交失败:', error)
    ElMessage.error('提交失败')
  } finally {
    submitting.value = false
  }
}

// 关闭对话框
const handleClose = () => {
  resetForm()
  dialogVisible.value = false
}

// 监听表单数据变化
watch(
  () => props.formData,
  (newData) => {
    if (newData && props.visible) {
      Object.assign(form, newData.info)
      // 处理文件列表
      if (newData.info.fileList) {
        fileList.value = newData.info.fileList.map((file) => ({
          name: file.fileName,
          url: file.url,
        }))
      }
    }
  },
  { immediate: true }
)

// 监听对话框显示状态
watch(
  () => props.visible,
  (visible) => {
    if (visible) {
      initDictData()
      if (!props.formData) {
        resetForm()
      }
    }
  }
)
</script>

<style scoped lang="scss">
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

:deep(.el-upload-dragger) {
  width: 100%;
}

:deep(.el-form-item__label) {
  font-weight: 500;
}
</style>
