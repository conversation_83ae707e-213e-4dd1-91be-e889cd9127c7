<template>
  <el-dialog
    v-model="dialogVisible"
    :title="isEdit ? '编辑核算系数库' : '新增核算系数库'"
    width="800px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <el-form ref="formRef" :model="form" :rules="rules" label-width="120px" label-position="left">
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="核算指南名称" prop="info.guidelineName">
            <el-input
              v-model="form.info.guidelineName"
              placeholder="请输入核算指南名称"
              maxlength="100"
              show-word-limit
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="发布部门" prop="info.issuingDepartment">
            <el-input
              v-model="form.info.issuingDepartment"
              placeholder="请输入发布部门"
              maxlength="100"
              show-word-limit
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="发布日期" prop="info.issueDate">
            <el-date-picker
              v-model="form.info.issueDate"
              type="date"
              placeholder="请选择发布日期"
              style="width: 100%"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="用途" prop="info.purpose">
            <el-select
              v-model="form.info.purpose"
              placeholder="请选择用途"
              multiple
              style="width: 100%"
            >
              <el-option
                v-for="item in purposeOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-form-item label="适用范围" prop="info.applicableScope">
        <el-input
          v-model="form.info.applicableScope"
          type="textarea"
          :rows="3"
          placeholder="请输入适用范围"
          maxlength="500"
          show-word-limit
        />
      </el-form-item>

      <el-form-item label="核算对象说明" prop="info.accountingEntity">
        <el-input
          v-model="form.info.accountingEntity"
          type="textarea"
          :rows="3"
          placeholder="请输入核算对象说明"
          maxlength="500"
          show-word-limit
        />
      </el-form-item>

      <el-form-item label="核算方法" prop="info.methodology">
        <el-input
          v-model="form.info.methodology"
          type="textarea"
          :rows="3"
          placeholder="请输入核算方法"
          maxlength="1000"
          show-word-limit
        />
      </el-form-item>

      <el-form-item label="关键公式" prop="info.keyFormulas">
        <el-input
          v-model="form.info.keyFormulas"
          type="textarea"
          :rows="3"
          placeholder="请输入关键公式"
          maxlength="1000"
          show-word-limit
        />
      </el-form-item>

      <el-form-item label="备注" prop="info.remarks">
        <el-input
          v-model="form.info.remarks"
          type="textarea"
          :rows="3"
          placeholder="请输入备注"
          maxlength="500"
          show-word-limit
        />
      </el-form-item>
      <el-form-item label="附件上传" prop="fileList.addFileIds" required>
        <el-upload
          ref="uploadRef"
          :action="uploadAction"
          :headers="uploadHeaders"
          :data="uploadData"
          :file-list="fileList"
          :on-success="handleUploadSuccess"
          :on-error="handleUploadError"
          :on-remove="handleFileRemove"
          :before-upload="beforeUpload"
          multiple
          drag
          class="upload-area"
        >
          <el-icon class="el-icon--upload"><upload-filled /></el-icon>
          <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
          <template #tip>
            <div class="el-upload__tip">
              支持上传 PDF、DOC、DOCX、XLS、XLSX 等格式文件，单个文件不超过 10MB
            </div>
          </template>
        </el-upload>
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button @click="handleSaveDraft" :loading="saving">保存草稿</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="submitting"> 提交 </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, watch, computed } from 'vue'
import { ElMessage, type FormInstance, type FormRules } from 'element-plus'
import { UploadFilled } from '@element-plus/icons-vue'
import type { GuidelineInfo, GuidelineFormData } from '@/api/types'
import { guidelineApi } from '@/api/knowledge'
import { fetchDictData } from '@/utils/options'
import { config } from '@/config/env'
// Props
interface Props {
  visible: boolean
  formData?: GuidelineFormData | null
  isEdit?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  formData: null,
  isEdit: false,
})

// Emits
const emit = defineEmits<{
  'update:visible': [value: boolean]
  success: []
}>()

// 响应式数据
const formRef = ref<FormInstance>()
const uploadRef = ref()
const saving = ref(false)
const submitting = ref(false)
const fileList = ref<any[]>([])
const purposeOptions = ref<any[]>([])

// 计算属性
const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value),
})

// 表单数据
const form = reactive({
  fileList: {
    addFileIds: [] as string[],
    delFileIds: [] as string[],
  },
  info: {
    guidelineId: '',
    guidelineName: '',
    issuingDepartment: '',
    issueDate: '',
    purpose: [],
    applicableScope: '',
    accountingEntity: '',
    methodology: '',
    keyFormulas: '',
    dataStatus: 2, // 默认暂存
    remarks: '',
  },
})

// 表单验证规则
const rules: FormRules = {
  'info.guidelineName': [
    { required: true, message: '请输入核算指南名称', trigger: 'blur' },
    { min: 1, max: 100, message: '长度在 1 到 100 个字符', trigger: 'blur' },
  ],
  'info.issuingDepartment': [
    { required: true, message: '请输入发布部门', trigger: 'blur' },
    { min: 1, max: 100, message: '长度在 1 到 100 个字符', trigger: 'blur' },
  ],
  'info.issueDate': [{ required: true, message: '请选择发布日期', trigger: 'change' }],
  'info.purpose': [{ required: true, message: '请选择用途', trigger: 'change' }],
  'info.applicableScope': [
    { required: true, message: '请输入适用范围', trigger: 'blur' },
    { min: 1, max: 500, message: '长度在 1 到 500 个字符', trigger: 'blur' },
  ],
  'info.accountingEntity': [
    { required: true, message: '请输入核算对象说明', trigger: 'blur' },
    { min: 1, max: 500, message: '长度在 1 到 500 个字符', trigger: 'blur' },
  ],
  'fileList.addFileIds': [
    {
      required: true,
      message: '请上传附件',
      trigger: 'change',
      validator: (rule: any, value: string[], callback: Function) => {
        if (form.fileList.addFileIds.length === 0) {
          callback(new Error('请至少上传一个附件'))
        } else {
          callback()
        }
      },
    },
  ],
}

// 初始化字典数据
const initDictData = async () => {
  try {
    purposeOptions.value = await fetchDictData('hsyt')
  } catch (error) {
    console.error('获取字典数据失败:', error)
  }
}

// 重置表单
const resetForm = () => {
  Object.assign(form, {
    fileList: {
      addFileIds: [] as string[],
      delFileIds: [] as string[],
    },
    info: {
      guidelineId: '',
      guidelineName: '',
      issuingDepartment: '',
      issueDate: '',
      purpose: [],
      applicableScope: '',
      accountingEntity: '',
      methodology: '',
      keyFormulas: '',
      dataStatus: 2, // 默认暂存
      remarks: '',
    },
  })
  fileList.value = []
  formRef.value?.clearValidate()
}

// 文件上传配置

const uploadAction = config.baseURL + '/File/upload'
const uploadHeaders = {
  Authorization: 'Bearer ' + (localStorage.getItem('token') || ''),
}
const uploadData = {
  type: 'guidelineInfo',
}
// 文件上传相关方法
const beforeUpload = (file: File) => {
  const allowedTypes = [
    'application/pdf',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'application/vnd.ms-excel',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
  ]

  const isAllowedType = allowedTypes.includes(file.type)
  const isLt10M = file.size / 1024 / 1024 < 10

  if (!isAllowedType) {
    ElMessage.error('只能上传 PDF、DOC、DOCX、XLS、XLSX 格式的文件!')
    return false
  }
  if (!isLt10M) {
    ElMessage.error('上传文件大小不能超过 10MB!')
    return false
  }
  return true
}

const handleUploadSuccess = (response: any, file: any, fileList: any) => {
  if (response.code === 200) {
    form.fileList.addFileIds.push(response.data.fileId)
    file.response = response.data
    ElMessage.success('文件上传成功')
    formRef.value?.validateField('fileList.addFileIds')
  } else {
    ElMessage.error(response.message || '文件上传失败')
    const index = fileList.findIndex((f: any) => f.uid === file.uid)
    if (index > -1) {
      fileList.splice(index, 1)
    }
  }
}

const handleUploadError = (error: any) => {
  ElMessage.error('文件上传失败: ' + error.message)
}

const handleFileRemove = (file: any) => {
  console.log('%c Line:483 🥚 file', 'color:#2eafb0', file)
  // 过滤文件里面对应的uid相同的数据

  form.fileList.delFileIds.push(String(file.uid))
  form.fileList.addFileIds = form.fileList.addFileIds.filter(
    (id: string) => id !== String(file.uid),
  )
  fileList.value = fileList.value.filter((f) => f.uid !== file.uid)
  // 如果文件有response数据，说明是上传成功的文件
  // const response = file.response as any
  // if (response && response.fileId) {
  //   const index = form.fileList.addFileIds.findIndex((id) => id === response.fileId)
  //   if (index > -1) {
  //     form.fileList.addFileIds.splice(index, 1)
  //   }
  // }
  // 触发表单验证
  formRef.value?.validateField('fileList.addFileIds')
}

// 保存草稿
const handleSaveDraft = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()
    saving.value = true

    console.log('%c Line:357 🥔 form', 'color:#2eafb0', form)
    const formData = {
      ...form,
      info: {
        ...form.info,
        dataStatus: 2,
      },
    }
    if (props.isEdit) {
      await guidelineApi.updateGuidelineInfo(formData)
    } else {
      await guidelineApi.saveGuidelineInfo(formData)
    }
    ElMessage.success('保存草稿成功')
    emit('success')
  } catch (error) {
    console.error('保存草稿失败:', error)
    ElMessage.error('保存草稿失败')
  } finally {
    saving.value = false
  }
}

// 提交
const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()
    submitting.value = true

    const submitData = {
      ...form,
      info: {
        ...form.info,
        dataStatus: 3,
      },
    }
    if (props.isEdit) {
      await guidelineApi.updateGuidelineInfo(submitData)
    } else {
      await guidelineApi.saveGuidelineInfo(submitData)
    }
    ElMessage.success('提交成功')
    emit('success')
  } catch (error) {
    console.error('提交失败:', error)
  } finally {
    submitting.value = false
  }
}

// 关闭对话框
const handleClose = () => {
  resetForm()
  dialogVisible.value = false
}

// 监听表单数据变化
watch(
  () => props.formData,
  (newData) => {
    if (newData && props.visible) {
      Object.assign(form, newData)
      // 处理文件列表
      if (newData.info.fileList) {
        fileList.value = newData.info.fileList.map((file) => ({
          name: file.fileName,
          url: file.url,
        }))
      }
    }
  },
  { immediate: true },
)

// 监听对话框显示状态
watch(
  () => props.visible,
  (visible) => {
    if (visible) {
      initDictData()
      if (!props.formData) {
        resetForm()
      }
    }
  },
)
</script>

<style scoped lang="scss">
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

:deep(.el-upload-dragger) {
  width: 100%;
}

:deep(.el-form-item__label) {
  font-weight: 500;
}
</style>
