# vite2

This template should help get you started developing with Vue 3 in Vite.

## Recommended IDE Setup

[VSCode](https://code.visualstudio.com/) + [Volar](https://marketplace.visualstudio.com/items?itemName=Vue.volar) (and disable Vetur).

## Type Support for `.vue` Imports in TS

TypeScript cannot handle type information for `.vue` imports by default, so we replace the `tsc` CLI with `vue-tsc` for type checking. In editors, we need [Volar](https://marketplace.visualstudio.com/items?itemName=Vue.volar) to make the TypeScript language service aware of `.vue` types.

## Customize configuration

See [Vite Configuration Reference](https://vite.dev/config/).

## Project Setup

```sh
pnpm install
```

### Compile and Hot-Reload for Development

```sh
pnpm run dev
```

### Type-Check, Compile and Minify for Production

```sh
pnpm run build
```

### Lint with [ESLint](https://eslint.org/)

```sh
pnpm run lint
```

你是一名尾部前段编程专家，现在我有以下需求：

1. 我要开发核算系数库菜单，该菜单是知识库管理的二级菜单；
2. 获取地区的接口是 /DictAll/getDistrict，处理数据label为name，value为code，type为value， children为children，并将数据保存到全局，也可以保存到本地,该接口在项目初始化即可调用。
3. 主要做增删改查功能，接下来我会把接口全部写出来：
   2-1. 获取核算系数库列表接口：/GuidelineInfo/GuidelineInfoList
   入参是：
      {
        "currentPage": 1,
        "pageSize": 2,
        "guidelineName": "", //核算指南名称
        "issueStartDate": "", //发布日期（开始，区间日期选择框），yyyy-MM-dd
        "issueEndDate": "", //发布日期（结束，区间日期选择框），yyyy-MM-dd
        "purpose": "", //用途（下拉，字典hsyt）
        "applicableScope": "", //适用范围
        "accountingEntity": "", //核算对象说明
        "dataStatus": 1 //数据状态（下拉，状态下拉）
      }
    出参是：
      {
        "code": 200,
        "msg": "success",
        "data": {
          "list": [
            {
              "guidelineId": "****************",
              "guidelineName": "1111",
              "issuingDepartment": "2222",
              "issueDate": "2025-09-10",
              "purpose": [
                "2"
              ],
              "applicableScope": "333",
              "accountingEntity": "444",
              "methodology": "555",
              "keyFormulas": "666",
              "inputUser": null,
              "inputTime": "2025-09-10 14:50:47",
              "updateUser": null,
              "updateTime": "2025-09-10 14:50:48",
              "dataStatus": 1,
              "remarks": "777",
              "purposeName": "环评预测核算",
              "dataStatusName": "正常",
              "fileList": [
                {
                  "fileId": "b6a71b94-c48b-4405-b9ca-6337e2b8e2d7",
                  "fileName": "yxkj.png",
                  "filePath": null,
                  "tableName": null,
                  "tableId": null,
                  "fileType": null,
                  "isActive": null,
                  "url": "File/getFile?fileName=79d03bf2-5d9c-4437-b579-3c37ec6847c2.png&type=guidelineInfo"
                }
              ]
            }
          ],
          "pagination": {
            "currentPage": 1,
            "pageSize": 2,
            "tableProp": "",
            "tableOrder": "",
            "total": 1
          }
        }
      }

   2-2. 新增核算系数库接口：/GuidelineInfo/saveGuidelineInfo
   入参是：
    {
          "info": {
            "guidelineName": "1111",
            "issuingDepartment": "2222",
            "issueDate": "2025-09-10",
            "purpose": [
              "2"
            ],
            "applicableScope": "333",
            "accountingEntity": "444",
            "methodology": "555",
            "keyFormulas": "666",
            "dataStatus": 2,
            "remarks": "ttttt"
          },
          "fileList": {
            "addFileIds": [
              "",
              ""
            ]
          }
    }

   2-3. 编辑核算系数库接口：/GuidelineInfo/updateGuidelineInfo
    {
      "info": {
        "guidelineId": "****************",
        "guidelineName": "********",
        "issuingDepartment": "2222",
        "issueDate": "2025-09-10",
        "purpose": [
          "2"
        ],
        "applicableScope": "333",
        "accountingEntity": "444",
        "methodology": "555",
        "keyFormulas": "666",
        "dataStatus": 2,
        "remarks": "ttttt"
      },
      "fileList": {
        "addFileIds": [
          "",
          ""
        ],
        "delFileIds": [
          "",
          ""
        ]
      }
    }
    
   2-4. 删除核算系数库接口：/GuidelineInfo/deleteGuidelineInfo
   入参是：
    {
      "info": {
        "guidelineId": "****************"
      }
    } 

   2-5. 获取状态下拉接口：/GuidelineInfo/getDataStatusList
   {
      "code": 200,
      "msg": "success",
      "data": [
        {
          "value": 2,
          "label": "暂存",
          "minLabel": null,
          "typeName": null,
          "parentValue": null,
          "unit": null,
          "remark": null
        },
        {
          "value": 3,
          "label": "待审核",
          "minLabel": null,
          "typeName": null,
          "parentValue": null,
          "unit": null,
          "remark": null
        },
        {
          "value": 1,
          "label": "正常",
          "minLabel": null,
          "typeName": null,
          "parentValue": null,
          "unit": null,
          "remark": null
        },
        {
          "value": -1,
          "label": "失效",
          "minLabel": null,
          "typeName": null,
          "parentValue": null,
          "unit": null,
          "remark": null
        }
      ]
    }

   2-6. 审核接口：/GuidelineInfo/approvedGuidelineInfo
   {
    "info": {
        "guidelineId": "****************",
        "dataStatus": 1
      }
    }

    2-7.获取用途的字典查询接口：/DictAll/getDict
    入参是：
    {
      "type": "hsyt"
    }

    
4. 保存为caog的时候dataStatus传2，提交的时候传3。
5. 将字典查询和地区最好做成公共方法处理，在需要的地方引入就可以，不用在额外处理。
6. 
7. 解决所有TS报错。
8. ，将数据请求回来，处理数据label为name，value为code，type为value，children为children，然后赋值给regionOptions。并将数据保存到全局，也可以保存到本地,该接口在登录完成后就可调用。
