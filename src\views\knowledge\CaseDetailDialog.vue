<template>
  <el-dialog v-model="visible" title="案例详情" width="900px" :close-on-click-modal="false">
    <div v-if="caseData" class="case-detail">
      <el-descriptions :column="2" border>
        <el-descriptions-item label="案例名称" :span="2">
          {{ caseData.caseName }}
        </el-descriptions-item>

        <el-descriptions-item label="适用地区">
          {{ caseData.applicableRegionName }}
        </el-descriptions-item>

        <el-descriptions-item label="问题类型">
          {{ caseData.caseProblemTypesName }}
        </el-descriptions-item>

        <el-descriptions-item label="实施目标" :span="2">
          {{ caseData.implementationTarget }}
        </el-descriptions-item>

        <el-descriptions-item label="实施期间" :span="2">
          {{ caseData.implementationPeriodName }}
        </el-descriptions-item>

        <el-descriptions-item label="应用对象">
          {{ caseData.applicationTarget }}
        </el-descriptions-item>

        <el-descriptions-item label="核心主题">
          {{ caseData.coreTheme }}
        </el-descriptions-item>

        <el-descriptions-item label="特色内容" :span="2">
          {{ caseData.featuredContent }}
        </el-descriptions-item>

        <el-descriptions-item label="案例来源">
          {{ caseData.caseSource }}
        </el-descriptions-item>

        <el-descriptions-item label="关联政策">
          {{ caseData.policyIdsName }}
        </el-descriptions-item>

        <el-descriptions-item label="数据状态">
          <el-tag :type="getStatusType(caseData.dataStatus)">
            {{ caseData.dataStatusName }}
          </el-tag>
        </el-descriptions-item>

        <el-descriptions-item label="更新时间">
          {{ caseData.updateTime }}
        </el-descriptions-item>

        <el-descriptions-item label="备注" :span="2">
          {{ caseData.remarks || '无' }}
        </el-descriptions-item>
      </el-descriptions>

      <!-- 附件列表 -->
      <div v-if="caseData.fileList && caseData.fileList.length > 0" class="file-section">
        <h4>附件列表</h4>
        <el-table :data="caseData.fileList" style="width: 100%">
          <el-table-column prop="fileName" label="文件名" />
          <!-- <el-table-column label="操作" width="180">
            <template #default="{ row }">
              <el-button size="small" type="primary" @click="downloadFile(row)"> 下载 </el-button>
              <el-button size="small" @click="previewFile(row)"> 预览 </el-button>
            </template>
          </el-table-column> -->
        </el-table>
      </div>

      <!-- 实施期间详情 -->
      <div
        v-if="caseData.implementationPeriod && caseData.implementationPeriod.length > 0"
        class="period-section"
      >
        <h4>实施期间详情</h4>
        <el-table :data="caseData.implementationPeriod" style="width: 100%">
          <el-table-column prop="startDate" label="开始日期" />
          <el-table-column prop="endDate" label="结束日期" />
          <el-table-column label="持续时间">
            <template #default="{ row }">
              {{ calculateDuration(row.startDate, row.endDate) }}
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="visible = false">关闭</el-button>
        <el-button type="primary" @click="handleEdit">编辑</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { ElMessage } from 'element-plus'
import type { CaseInfo, CaseFile } from '@/api/types'

interface Props {
  modelValue: boolean
  caseData?: CaseInfo | null
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
  (e: 'edit', data: CaseInfo): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 计算属性
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value),
})

// 方法
const getStatusType = (status: number) => {
  switch (status) {
    case 1:
      return 'success'
    case 0:
      return 'warning'
    default:
      return 'info'
  }
}

const calculateDuration = (startDate: string, endDate: string) => {
  if (!startDate || !endDate) return '未知'

  const start = new Date(startDate)
  const end = new Date(endDate)
  const diffTime = Math.abs(end.getTime() - start.getTime())
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))

  if (diffDays < 30) {
    return `${diffDays}天`
  } else if (diffDays < 365) {
    const months = Math.floor(diffDays / 30)
    return `${months}个月`
  } else {
    const years = Math.floor(diffDays / 365)
    const remainingMonths = Math.floor((diffDays % 365) / 30)
    return remainingMonths > 0 ? `${years}年${remainingMonths}个月` : `${years}年`
  }
}

const downloadFile = (file: CaseFile) => {
  if (file.url) {
    // 创建一个临时的a标签来下载文件
    const link = document.createElement('a')
    link.href = file.url
    link.download = file.fileName
    link.target = '_blank'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
  } else {
    ElMessage.error('文件链接不存在')
  }
}

const previewFile = (file: CaseFile) => {
  if (file.url) {
    // 在新窗口中打开文件进行预览
    window.open(file.url, '_blank')
  } else {
    ElMessage.error('文件链接不存在')
  }
}

const handleEdit = () => {
  if (props.caseData) {
    emit('edit', props.caseData)
    visible.value = false
  }
}
</script>

<style scoped>
.case-detail {
  max-height: 600px;
  overflow-y: auto;
}

.file-section,
.period-section {
  margin-top: 20px;
}

.file-section h4,
.period-section h4 {
  margin-bottom: 10px;
  color: #303133;
  font-weight: 600;
}

.dialog-footer {
  text-align: right;
}

:deep(.el-descriptions__label) {
  font-weight: 600;
  color: #606266;
}

:deep(.el-descriptions__content) {
  color: #303133;
}
</style>
