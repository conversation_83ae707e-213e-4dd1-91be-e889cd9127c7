// 通用响应接口
export interface ApiResponse<T = any> {
  code: number
  msg: string
  data: T
  success?: boolean
}

// 政策信息搜索参数
export interface PolicyInfoSearchParams extends BaseSearchParams {
  currentPage: number
  pageSize: number
  policyName: string //政策名称（文本框）
  publishStartDate: string //发布日期（开始，区间日期选择框），yyyy-MM-dd
  publishEndDate: string //发布日期（结束，区间日期选择框），yyyy-MM-dd
  policyLevel: string //政策层级（下拉，字典zccj）
  applicableRegion: string //适用地区（下拉，适用地区字典）
  policyType: string //政策类型（下拉，字典zclx）
  policyNumber: string //政策编号（文本框）
  controlledPollutants: string //涉及污染物（下拉，字典sjwrw）
  dataStatus: string //数据状态（下拉，状态下拉）
}

// 分页请求参数
export interface PageParams {
  page?: number
  pageSize?: number
  current?: number
  size?: number
}

// 分页响应数据
export interface PageResult<T = any> {
  list: T[]
  total: number
  current: number
  size: number
  pages?: number
  records?: T[]
}

// 用户相关类型
export interface User {
  id: string | number
  username: string
  nickname?: string
  email?: string
  phone?: string
  avatar?: string
  roles?: string[]
  permissions?: string[]
  createTime?: string
  updateTime?: string
}

// 登录请求参数
export interface LoginParams {
  username: string
  password: string
  captcha?: string
  rememberMe?: boolean
}

// 登录响应数据
export interface LoginResult {
  token: string
  refreshToken?: string
  userInfo: User
  expires?: number
}

// 知识库案例类型
export interface ExcellentCase {
  id: string | number
  title: string
  category: string
  region: string
  effect: string
  content?: string
  attachments?: string[]
  tags?: string[]
  status: 'draft' | 'published' | 'archived'
  createTime: string
  updateTime?: string
  creator: string
  viewCount?: number
}

// 政策信息类型
export interface PolicyInfo {
  id: string | number
  title: string
  type: string
  region: string
  publishTime: string
  content?: string
  attachments?: string[]
  status: 'active' | 'inactive' | 'expired'
  source?: string
  tags?: string[]
  createTime: string
  updateTime?: string
}

// 仓库信息类型
export interface WarehouseInfo {
  id: string | number
  code: string
  name: string
  label: string
  value: string
  type?: string
  address?: string
  status: 'active' | 'inactive'
  createTime?: string
}

// 入库单类型
export interface EntryOrder {
  id: string | number
  entryNo: string
  source: 'manual' | 'system' | 'import'
  businessType: 'purchase' | 'return' | 'transfer'
  status: 'pending' | 'approved' | 'rejected'
  amount: number
  warehouseId?: string | number
  warehouseName?: string
  createTime: string
  updateTime?: string
  creator: string
  remark?: string
}

// 文件上传响应
export interface UploadResult {
  url: string
  filename: string
  size: number
  type: string
}

// 字典数据类型
export interface DictData {
  label: string
  value: string | number
  type?: string
  sort?: number
  remark?: string
  status?: 'active' | 'inactive'
}

// 搜索参数基础类型
export interface BaseSearchParams extends PageParams {
  keyword?: string
  startTime?: string
  endTime?: string
  status?: string
  createTimeRange?: string[]
}

// 优秀案例搜索参数
export interface ExcellentCaseSearchParams {
  currentPage: number
  pageSize: number
  caseName?: string // 案例名称
  applicableRegion?: string // 适用地区（下拉，适用地区字典）
  caseProblemTypes?: string // 案例问题类型（下拉，字典alwtlx）
  updateDate?: string[] // 更新时间（区间日期选择框），yyyy-MM-dd HH:mm:ss
  updateStartDate?: string // 更新时间（开始，区间日期选择框），yyyy-MM-dd HH:mm:ss
  updateEndDate?: string // 更新时间（结束，区间日期选择框），yyyy-MM-dd HH:mm:ss
  dataStatus?: number // 数据状态（下拉，状态下拉）
}

// 优秀案例实施期间
export interface ImplementationPeriod {
  startDate: string
  endDate: string
}

// 优秀案例文件信息
export interface CaseFile {
  fileId: string
  fileName: string
  filePath?: string
  tableName?: string
  tableId?: string
  fileType?: string
  isActive?: boolean
  url: string
}

// 优秀案例信息
export interface CaseInfo {
  caseId?: string
  caseName: string
  applicableRegion: string[]
  implementationTarget: string
  implementationPeriod: ImplementationPeriod[]
  caseProblemTypes: string[]
  applicationTarget: string
  coreTheme: string
  featuredContent: string
  caseSource: string
  policyIds: string[]
  inputUser?: string
  inputTime?: string
  updateUser?: string
  updateTime?: string
  dataStatus: number
  remarks?: string
  // 显示用的名称字段
  applicableRegionName?: string
  caseProblemTypesName?: string
  implementationPeriodName?: string
  policyIdsName?: string
  dataStatusName?: string
  fileList?: any
}

// 优秀案例列表响应数据
export interface CaseInfoListResponse {
  list: CaseInfo[]
  pagination: {
    currentPage: number
    pageSize: number
    tableProp: string
    tableOrder: string
    total: number
  }
}

// 优秀案例表单数据
export interface CaseFormData {
  info: CaseInfo
  fileList: {
    addFileIds: string[]
  }
}

// 优秀案例审批数据
export interface CaseApprovalData {
  info: {
    caseId: string
    dataStatus: number | string
  }
}

// 优秀案例删除数据
export interface CaseDeleteData {
  info: {
    caseId: string
  }
}

// 入库单搜索参数
export interface EntryOrderSearchParams extends BaseSearchParams {
  entryNo?: string
  source?: string
  businessType?: string
  warehouseId?: string | number
  creator?: string
  amountRange?: [number, number]
}

// ==================== 核算系数库相关类型 ====================

// 核算系数库搜索参数
export interface GuidelineInfoSearchParams {
  currentPage: number
  pageSize: number
  guidelineName?: string // 核算指南名称
  issueStartDate?: string // 发布日期（开始，区间日期选择框），yyyy-MM-dd
  issueEndDate?: string // 发布日期（结束，区间日期选择框），yyyy-MM-dd
  purpose?: string // 用途（下拉，字典hsyt）
  applicableScope?: string // 适用范围
  accountingEntity?: string // 核算对象说明
  dataStatus?: number | string // 数据状态（下拉，状态下拉）
  [key: string]: any
}

// 核算系数库文件信息
export interface GuidelineFile {
  fileId: string
  fileName: string
  filePath?: string
  tableName?: string
  tableId?: string
  fileType?: string
  isActive?: boolean
  url: string
}

// 核算系数库信息
export interface GuidelineInfo {
  guidelineId?: string
  guidelineName: string
  issuingDepartment: string
  issueDate: string
  purpose: string[]
  applicableScope: string
  accountingEntity: string
  methodology: string
  keyFormulas: string
  inputUser?: string
  inputTime?: string
  updateUser?: string
  updateTime?: string
  dataStatus: number
  remarks?: string
  // 显示用的名称字段
  purposeName?: string
  dataStatusName?: string
  fileList?: GuidelineFile[]
}

// 核算系数库列表响应数据
export interface GuidelineInfoListResponse {
  list: GuidelineInfo[]
  pagination: {
    currentPage: number
    pageSize: number
    tableProp: string
    tableOrder: string
    total: number
  }
}

// 核算系数库表单数据
export interface GuidelineFormData {
  info: GuidelineInfo
  fileList: {
    addFileIds: string[]
    delFileIds?: string[]
  }
}

// 核算系数库审批数据
export interface GuidelineApprovalData {
  info: {
    guidelineId: string
    dataStatus: number | string
  }
}

// 核算系数库删除数据
export interface GuidelineDeleteData {
  info: {
    guidelineId: string
  }
}
