// 通用响应接口
export interface ApiResponse<T = any> {
  code: number
  msg: string
  data: T
  success?: boolean
}

// 政策信息搜索参数
export interface PolicyInfoSearchParams extends BaseSearchParams {
  currentPage: number
  pageSize: number
  policyName: string //政策名称（文本框）
  publishStartDate: string //发布日期（开始，区间日期选择框），yyyy-MM-dd
  publishEndDate: string //发布日期（结束，区间日期选择框），yyyy-MM-dd
  policyLevel: string //政策层级（下拉，字典zccj）
  applicableRegion: string //适用地区（下拉，适用地区字典）
  policyType: string //政策类型（下拉，字典zclx）
  policyNumber: string //政策编号（文本框）
  controlledPollutants: string //涉及污染物（下拉，字典sjwrw）
  dataStatus: string //数据状态（下拉，状态下拉）
}

// 分页请求参数
export interface PageParams {
  page?: number
  pageSize?: number
  current?: number
  size?: number
}

// 分页响应数据
export interface PageResult<T = any> {
  list: T[]
  total: number
  current: number
  size: number
  pages?: number
  records?: T[]
}

// 用户相关类型
export interface User {
  id: string | number
  username: string
  nickname?: string
  email?: string
  phone?: string
  avatar?: string
  roles?: string[]
  permissions?: string[]
  createTime?: string
  updateTime?: string
}

// 登录请求参数
export interface LoginParams {
  username: string
  password: string
  captcha?: string
  rememberMe?: boolean
}

// 登录响应数据
export interface LoginResult {
  token: string
  refreshToken?: string
  userInfo: User
  expires?: number
}

// 知识库案例类型
export interface ExcellentCase {
  id: string | number
  title: string
  category: string
  region: string
  effect: string
  content?: string
  attachments?: string[]
  tags?: string[]
  status: 'draft' | 'published' | 'archived'
  createTime: string
  updateTime?: string
  creator: string
  viewCount?: number
}

// 政策信息类型
export interface PolicyInfo {
  id: string | number
  title: string
  type: string
  region: string
  publishTime: string
  content?: string
  attachments?: string[]
  status: 'active' | 'inactive' | 'expired'
  source?: string
  tags?: string[]
  createTime: string
  updateTime?: string
}

// 仓库信息类型
export interface WarehouseInfo {
  id: string | number
  code: string
  name: string
  label: string
  value: string
  type?: string
  address?: string
  status: 'active' | 'inactive'
  createTime?: string
}

// 入库单类型
export interface EntryOrder {
  id: string | number
  entryNo: string
  source: 'manual' | 'system' | 'import'
  businessType: 'purchase' | 'return' | 'transfer'
  status: 'pending' | 'approved' | 'rejected'
  amount: number
  warehouseId?: string | number
  warehouseName?: string
  createTime: string
  updateTime?: string
  creator: string
  remark?: string
}

// 文件上传响应
export interface UploadResult {
  url: string
  filename: string
  size: number
  type: string
}

// 字典数据类型
export interface DictData {
  label: string
  value: string | number
  type?: string
  sort?: number
  remark?: string
  status?: 'active' | 'inactive'
}

// 搜索参数基础类型
export interface BaseSearchParams extends PageParams {
  keyword?: string
  startTime?: string
  endTime?: string
  status?: string
  createTimeRange?: string[]
}

// 优秀案例搜索参数
export interface ExcellentCaseSearchParams {
  currentPage: number
  pageSize: number
  caseName?: string // 案例名称
  applicableRegion?: string // 适用地区（下拉，适用地区字典）
  caseProblemTypes?: string // 案例问题类型（下拉，字典alwtlx）
  updateDate?: string[] // 更新时间（区间日期选择框），yyyy-MM-dd HH:mm:ss
  updateStartDate?: string // 更新时间（开始，区间日期选择框），yyyy-MM-dd HH:mm:ss
  updateEndDate?: string // 更新时间（结束，区间日期选择框），yyyy-MM-dd HH:mm:ss
  dataStatus?: number // 数据状态（下拉，状态下拉）
}

// 优秀案例实施期间
export interface ImplementationPeriod {
  startDate: string
  endDate: string
}

// 优秀案例文件信息
export interface CaseFile {
  fileId: string
  fileName: string
  filePath?: string
  tableName?: string
  tableId?: string
  fileType?: string
  isActive?: boolean
  url: string
}

// 优秀案例信息
export interface CaseInfo {
  caseId?: string
  caseName: string
  applicableRegion: string[]
  implementationTarget: string
  implementationPeriod: ImplementationPeriod[]
  caseProblemTypes: string[]
  applicationTarget: string
  coreTheme: string
  featuredContent: string
  caseSource: string
  policyIds: string[]
  inputUser?: string
  inputTime?: string
  updateUser?: string
  updateTime?: string
  dataStatus: number
  remarks?: string
  // 显示用的名称字段
  applicableRegionName?: string
  caseProblemTypesName?: string
  implementationPeriodName?: string
  policyIdsName?: string
  dataStatusName?: string
  fileList?: any
}

// 优秀案例列表响应数据
export interface CaseInfoListResponse {
  list: CaseInfo[]
  pagination: {
    currentPage: number
    pageSize: number
    tableProp: string
    tableOrder: string
    total: number
  }
}

// 优秀案例表单数据
export interface CaseFormData {
  info: CaseInfo
  fileList: {
    addFileIds: string[]
  }
}

// 优秀案例审批数据
export interface CaseApprovalData {
  info: {
    caseId: string
    dataStatus: number | string
  }
}

// 优秀案例删除数据
export interface CaseDeleteData {
  info: {
    caseId: string
  }
}

// 入库单搜索参数
export interface EntryOrderSearchParams extends BaseSearchParams {
  entryNo?: string
  source?: string
  businessType?: string
  warehouseId?: string | number
  creator?: string
  amountRange?: [number, number]
}
