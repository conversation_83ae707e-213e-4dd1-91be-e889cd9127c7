import { ElMessage } from 'element-plus'

// 支持的文件类型
export const SUPPORTED_FILE_TYPES = {
  PDF: 'application/pdf',
  DOC: 'application/msword',
  DOCX: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
  XLS: 'application/vnd.ms-excel',
  XLSX: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
}

// 支持的文件扩展名
export const SUPPORTED_EXTENSIONS = ['.pdf', '.doc', '.docx', '.xls', '.xlsx']

// 文件信息接口
export interface FileInfo {
  fileId: string
  fileName: string
  filePath?: string
  url: string
  fileType?: string
  size?: number
}

/**
 * 获取文件扩展名
 * @param fileName 文件名
 * @returns 文件扩展名（小写）
 */
export const getFileExtension = (fileName: string): string => {
  const lastDotIndex = fileName.lastIndexOf('.')
  return lastDotIndex !== -1 ? fileName.substring(lastDotIndex).toLowerCase() : ''
}

/**
 * 检查文件类型是否支持
 * @param fileName 文件名
 * @returns 是否支持
 */
export const isSupportedFileType = (fileName: string): boolean => {
  const extension = getFileExtension(fileName)
  return SUPPORTED_EXTENSIONS.includes(extension)
}

/**
 * 获取文件类型描述
 * @param fileName 文件名
 * @returns 文件类型描述
 */
export const getFileTypeDescription = (fileName: string): string => {
  const extension = getFileExtension(fileName).toLowerCase()
  switch (extension) {
    case '.pdf':
      return 'PDF文档'
    case '.doc':
      return 'Word文档'
    case '.docx':
      return 'Word文档'
    case '.xls':
      return 'Excel表格'
    case '.xlsx':
      return 'Excel表格'
    default:
      return '未知文件'
  }
}

/**
 * 格式化文件大小
 * @param bytes 字节数
 * @returns 格式化后的文件大小
 */
export const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 B'

  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))

  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

/**
 * 全局文件预览方法
 * @param file 文件信息
 * @param options 预览选项
 */
export const previewFile = (
  file: FileInfo,
  options?: {
    newWindow?: boolean
    width?: number
    height?: number
  },
): void => {
  try {
    if (!file.url) {
      ElMessage.warning('文件预览链接不可用')
      return
    }

    if (!isSupportedFileType(file.fileName)) {
      ElMessage.warning(`不支持预览 ${getFileExtension(file.fileName)} 格式的文件`)
      return
    }

    const { newWindow = true, width = 1200, height = 800 } = options || {}

    // 构建预览URL
    let previewUrl = file.url

    // 对于Office文档，可以使用在线预览服务
    const extension = getFileExtension(file.fileName)
    if (['.doc', '.docx', '.xls', '.xlsx'].includes(extension)) {
      // 可以集成微软Office在线预览或其他在线预览服务
      // 这里使用简单的下载方式，实际项目中可以替换为在线预览服务
      previewUrl = file.url
    }

    if (newWindow) {
      // 在新窗口中打开
      const windowFeatures = `width=${width},height=${height},scrollbars=yes,resizable=yes,toolbar=no,menubar=no,location=no,status=no`
      const previewWindow = window.open(previewUrl, '_blank', windowFeatures)

      if (!previewWindow) {
        ElMessage.error('无法打开预览窗口，请检查浏览器弹窗设置')
      }
    } else {
      // 在当前窗口中打开
      window.location.href = previewUrl
    }

    ElMessage.success('正在打开文件预览...')
  } catch (error) {
    console.error('文件预览失败:', error)
    ElMessage.error('文件预览失败，请稍后重试')
  }
}

/**
 * 全局文件下载方法
 * @param file 文件信息
 * @param options 下载选项
 */
export const downloadFile = (
  file: FileInfo,
  options?: {
    customFileName?: string
    showProgress?: boolean
  },
): void => {
  try {
    if (!file.url) {
      ElMessage.warning('文件下载链接不可用')
      return
    }

    if (!isSupportedFileType(file.fileName)) {
      ElMessage.warning(`不支持下载 ${getFileExtension(file.fileName)} 格式的文件`)
      return
    }

    const { customFileName, showProgress = true } = options || {}

    if (showProgress) {
      ElMessage.info('正在准备下载...')
    }

    // 创建隐藏的下载链接
    const link = document.createElement('a')
    link.href = file.url
    link.download = customFileName || file.fileName
    link.style.display = 'none'

    // 添加到DOM并触发点击
    document.body.appendChild(link)
    link.click()

    // 清理DOM
    document.body.removeChild(link)

    if (showProgress) {
      ElMessage.success('文件下载已开始')
    }
  } catch (error) {
    console.error('文件下载失败:', error)
    ElMessage.error('文件下载失败，请稍后重试')
  }
}

/**
 * 批量下载文件
 * @param files 文件列表
 * @param options 下载选项
 */
export const downloadFiles = (
  files: FileInfo[],
  options?: {
    delay?: number
    showProgress?: boolean
  },
): void => {
  try {
    if (!files || files.length === 0) {
      ElMessage.warning('没有可下载的文件')
      return
    }

    const { delay = 500, showProgress = true } = options || {}

    if (showProgress) {
      ElMessage.info(`正在下载 ${files.length} 个文件...`)
    }

    // 依次下载文件，避免浏览器阻止多个下载
    files.forEach((file, index) => {
      setTimeout(() => {
        downloadFile(file, { showProgress: false })
      }, index * delay)
    })

    if (showProgress) {
      ElMessage.success('批量下载已开始')
    }
  } catch (error) {
    console.error('批量下载失败:', error)
    ElMessage.error('批量下载失败，请稍后重试')
  }
}

/**
 * 验证文件上传
 * @param file 文件对象
 * @param options 验证选项
 * @returns 验证结果
 */
export const validateFileUpload = (
  file: File,
  options?: {
    maxSize?: number // 最大文件大小（字节）
    allowedTypes?: string[] // 允许的文件类型
  },
): { valid: boolean; message?: string } => {
  const { maxSize = 10 * 1024 * 1024, allowedTypes = SUPPORTED_EXTENSIONS } = options || {}

  // 检查文件大小
  if (file.size > maxSize) {
    return {
      valid: false,
      message: `文件大小不能超过 ${formatFileSize(maxSize)}`,
    }
  }

  // 检查文件类型
  const extension = getFileExtension(file.name)
  if (!allowedTypes.includes(extension)) {
    return {
      valid: false,
      message: `不支持 ${extension} 格式的文件，支持的格式：${allowedTypes.join(', ')}`,
    }
  }

  return { valid: true }
}

// 导出常用的文件操作方法
export default {
  previewFile,
  downloadFile,
  downloadFiles,
  validateFileUpload,
  getFileExtension,
  isSupportedFileType,
  getFileTypeDescription,
  formatFileSize,
  SUPPORTED_FILE_TYPES,
  SUPPORTED_EXTENSIONS,
}
