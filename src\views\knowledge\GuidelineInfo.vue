<template>
  <div class="guideline-info-container">
    <!-- 搜索表单 -->
    <CollapsePanel title="搜索条件">
      <SearchForm
        :form-items="searchFormItems"
        :form-data="searchForm"
        @search="handleSearch"
        @reset="handleReset"
      />
    </CollapsePanel>

    <!-- 操作按钮 -->
    <div class="operation-bar">
      <el-button type="primary" :icon="Plus" @click="handleAdd">新增</el-button>
    </div>

    <!-- 列表 -->
    <div class="list-container">
      <div v-if="loading" class="loading-container">
        <el-skeleton :rows="5" animated />
      </div>
      <div v-else-if="guidelineList.length === 0" class="empty-container">
        <el-empty description="暂无数据" />
      </div>
      <div v-else class="guideline-list">
        <div v-for="item in guidelineList" :key="item.guidelineId" class="guideline-item">
          <div class="guideline-header">
            <h3 class="guideline-title">{{ item.guidelineName }}</h3>
            <el-tag :type="getStatusTagType(item.dataStatus)" class="status-tag">
              {{ item.dataStatusName }}
            </el-tag>
          </div>

          <div class="guideline-content">
            <p class="guideline-description">{{ item.applicableScope }}</p>
          </div>

          <div class="guideline-meta">
            <span class="meta-item">
              <el-icon><Calendar /></el-icon>
              {{ item.issueDate }}
            </span>
            <span class="meta-item">
              <el-icon><Document /></el-icon>
              PDF {{ getFileSize(item.fileList) }}
            </span>
          </div>

          <div class="guideline-actions">
            <el-button type="primary" size="small" @click="handleView(item)">
              <el-icon><View /></el-icon>
              在线预览
            </el-button>
            <el-button size="small" @click="handleDownload(item)">
              <el-icon><Download /></el-icon>
              下载
            </el-button>
            <el-dropdown @command="(command) => handleDropdownCommand(command, item)">
              <el-button size="small">
                更多
                <el-icon class="el-icon--right"><ArrowDown /></el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="edit">编辑</el-dropdown-item>
                  <el-dropdown-item command="approve" v-if="item.dataStatus === 3"
                    >审核</el-dropdown-item
                  >
                  <el-dropdown-item command="delete" divided>删除</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
        </div>
      </div>
    </div>

    <!-- 分页 -->
    <div class="pagination-container">
      <el-pagination
        v-model:current-page="pagination.currentPage"
        v-model:page-size="pagination.pageSize"
        :page-sizes="[10, 20, 50, 100]"
        :total="pagination.total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 表单对话框 -->
    <GuidelineFormDialog
      v-model:visible="formDialogVisible"
      :form-data="currentFormData"
      :is-edit="isEdit"
      @success="handleFormSuccess"
    />

    <!-- 详情对话框 -->
    <GuidelineDetailDialog
      v-model:visible="detailDialogVisible"
      :guideline-data="currentGuidelineData"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Search,
  Plus,
  Calendar,
  Document,
  View,
  Download,
  ArrowDown,
} from '@element-plus/icons-vue'
import CollapsePanel from '@/components/CollapsePanel.vue'
import SearchForm from '@/components/SearchForm.vue'
import type { FormItem } from '@/components/SearchForm.vue'
import type { GuidelineInfo, GuidelineInfoSearchParams, GuidelineFormData } from '@/api/types'
import { guidelineApi } from '@/api/guideline'
import { fetchDictData, fetchDistrictData } from '@/utils/options'
import GuidelineFormDialog from './GuidelineFormDialog.vue'
import GuidelineDetailDialog from './GuidelineDetailDialog.vue'

// 响应式数据
const loading = ref(false)
const guidelineList = ref<GuidelineInfo[]>([])
const formDialogVisible = ref(false)
const detailDialogVisible = ref(false)
const isEdit = ref(false)
const currentFormData = ref<GuidelineFormData | null>(null)
const currentGuidelineData = ref<GuidelineInfo | null>(null)

// 搜索表单
const searchForm = reactive<GuidelineInfoSearchParams>({
  currentPage: 1,
  pageSize: 20,
  guidelineName: '',
  issueStartDate: '',
  issueEndDate: '',
  purpose: '',
  applicableScope: '',
  accountingEntity: '',
  dataStatus: 1,
})

// 分页数据
const pagination = reactive({
  currentPage: 1,
  pageSize: 20,
  total: 0,
})

// 字典数据
const purposeOptions = ref<any[]>([])
const statusOptions = ref<any[]>([])

// 搜索表单配置
// 搜索表单配置
const searchConfig = ref<FormItem[]>([
  {
    type: 'input',
    prop: 'policyName',
    formItem: { label: '政策名称' },
    attrs: { placeholder: '请输入政策名称', clearable: true },
  },
  {
    type: 'input',
    prop: 'policyNumber',
    formItem: { label: '政策编号' },
    attrs: { placeholder: '请输入政策编号', clearable: true },
  },
  {
    type: 'select',
    prop: 'policyType',
    formItem: { label: '政策类型' },
    attrs: {
      placeholder: '请选择政策类型',
      clearable: true,
      options: [],
    },
  },
  {
    type: 'select',
    prop: 'policyLevel',
    formItem: { label: '政策级别' },
    attrs: {
      placeholder: '请选择政策级别',
      options: [],
      clearable: true,
    },
  },
  {
    type: 'select',
    prop: 'controlledPollutants',
    formItem: { label: '管控污染物' },
    attrs: { placeholder: '请输入管控污染物', multiple: true, clearable: true },
  },
  {
    type: 'daterange',
    prop: 'publishDate',
    formItem: { label: '发布时间' },
    attrs: {
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD',
      startPlaceholder: '开始日期',
      endPlaceholder: '结束日期',
      clearable: true,
    },
  },
  {
    type: 'select',
    prop: 'dataStatus',
    formItem: { label: '数据状态' },
    attrs: {
      placeholder: '请选择数据状态',
      options: dataStatusOptions,
      clearable: true,
    },
  },
])

// 初始化字典数据
const initDictData = async () => {
  try {
    // 获取用途字典
    purposeOptions.value = await fetchDictData('hsyt')

    // 获取状态字典
    const statusRes = await guidelineApi.getDataStatusList()
    statusOptions.value = statusRes.data || []
  } catch (error) {
    console.error('获取字典数据失败:', error)
  }
}

// 获取列表数据
const fetchGuidelineList = async () => {
  loading.value = true
  try {
    const params = {
      ...searchForm,
      currentPage: pagination.currentPage,
      pageSize: pagination.pageSize,
    }

    // 处理日期范围
    if (searchForm.issueStartDate && searchForm.issueEndDate) {
      params.issueStartDate = searchForm.issueStartDate
      params.issueEndDate = searchForm.issueEndDate
    }

    const response = await guidelineApi.getGuidelineInfoList(params)
    if (response.code === 200) {
      guidelineList.value = response.data.list || []
      pagination.total = response.data.pagination?.total || 0
    }
  } catch (error) {
    console.error('获取核算系数库列表失败:', error)
    ElMessage.error('获取数据失败')
  } finally {
    loading.value = false
  }
}

// 搜索
const handleSearch = (formData: any) => {
  Object.assign(searchForm, formData)

  // 处理日期范围
  if (formData.issueDate && formData.issueDate.length === 2) {
    searchForm.issueStartDate = formData.issueDate[0]
    searchForm.issueEndDate = formData.issueDate[1]
  } else {
    searchForm.issueStartDate = ''
    searchForm.issueEndDate = ''
  }

  pagination.currentPage = 1
  fetchGuidelineList()
}

// 重置搜索
const handleReset = () => {
  Object.assign(searchForm, {
    currentPage: 1,
    pageSize: 20,
    guidelineName: '',
    issueStartDate: '',
    issueEndDate: '',
    purpose: '',
    applicableScope: '',
    accountingEntity: '',
    dataStatus: 1,
  })
  pagination.currentPage = 1
  fetchGuidelineList()
}

// 新增
const handleAdd = () => {
  isEdit.value = false
  currentFormData.value = null
  formDialogVisible.value = true
}

// 编辑
const handleEdit = (item: GuidelineInfo) => {
  isEdit.value = true
  currentFormData.value = {
    info: { ...item },
    fileList: {
      addFileIds: [],
    },
  }
  formDialogVisible.value = true
}

// 查看详情
const handleView = (item: GuidelineInfo) => {
  currentGuidelineData.value = item
  detailDialogVisible.value = true
}

// 下载
const handleDownload = (item: GuidelineInfo) => {
  if (item.fileList && item.fileList.length > 0) {
    const file = item.fileList[0]
    window.open(file.url, '_blank')
  } else {
    ElMessage.warning('暂无可下载的文件')
  }
}

// 下拉菜单命令处理
const handleDropdownCommand = (command: string, item: GuidelineInfo) => {
  switch (command) {
    case 'edit':
      handleEdit(item)
      break
    case 'approve':
      handleApprove(item)
      break
    case 'delete':
      handleDelete(item)
      break
  }
}

// 审核
const handleApprove = async (item: GuidelineInfo) => {
  try {
    await ElMessageBox.confirm('确认审核通过该核算系数库吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })

    await guidelineApi.approvedGuidelineInfo({
      info: {
        guidelineId: item.guidelineId!,
        dataStatus: 1,
      },
    })

    ElMessage.success('审核成功')
    fetchGuidelineList()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('审核失败:', error)
      ElMessage.error('审核失败')
    }
  }
}

// 删除
const handleDelete = async (item: GuidelineInfo) => {
  try {
    await ElMessageBox.confirm('确认删除该核算系数库吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })

    await guidelineApi.deleteGuidelineInfo({
      info: {
        guidelineId: item.guidelineId!,
      },
    })

    ElMessage.success('删除成功')
    fetchGuidelineList()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除失败:', error)
      ElMessage.error('删除失败')
    }
  }
}

// 表单提交成功
const handleFormSuccess = () => {
  formDialogVisible.value = false
  fetchGuidelineList()
}

// 分页大小改变
const handleSizeChange = (size: number) => {
  pagination.pageSize = size
  pagination.currentPage = 1
  fetchGuidelineList()
}

// 当前页改变
const handleCurrentChange = (page: number) => {
  pagination.currentPage = page
  fetchGuidelineList()
}

// 获取状态标签类型
const getStatusTagType = (status: number) => {
  switch (status) {
    case 1:
      return 'success' // 正常
    case 2:
      return 'info' // 暂存
    case 3:
      return 'warning' // 待审核
    case -1:
      return 'danger' // 失效
    default:
      return 'info'
  }
}

// 获取文件大小
const getFileSize = (fileList?: any[]) => {
  if (!fileList || fileList.length === 0) return '0MB'
  // 这里可以根据实际文件信息计算大小，暂时返回固定值
  return '2.4MB'
}

// 组件挂载
onMounted(() => {
  initDictData()
  fetchGuidelineList()
})
</script>

<style scoped lang="scss">
.guideline-info-container {
  padding: 20px;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.operation-bar {
  margin: 20px 0;
  display: flex;
  justify-content: flex-start;
}

.list-container {
  background: white;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
}

.loading-container,
.empty-container {
  padding: 40px 0;
  text-align: center;
}

.guideline-list {
  .guideline-item {
    background: white;
    border: 1px solid #e4e7ed;
    border-radius: 8px;
    padding: 24px;
    margin-bottom: 16px;
    transition: all 0.3s ease;

    &:hover {
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      border-color: #409eff;
    }

    .guideline-header {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      margin-bottom: 16px;

      .guideline-title {
        font-size: 18px;
        font-weight: 600;
        color: #303133;
        margin: 0;
        flex: 1;
        margin-right: 16px;
      }

      .status-tag {
        flex-shrink: 0;
      }
    }

    .guideline-content {
      margin-bottom: 16px;

      .guideline-description {
        color: #606266;
        line-height: 1.6;
        margin: 0;
      }
    }

    .guideline-meta {
      display: flex;
      gap: 24px;
      margin-bottom: 16px;

      .meta-item {
        display: flex;
        align-items: center;
        gap: 4px;
        color: #909399;
        font-size: 14px;

        .el-icon {
          font-size: 16px;
        }
      }
    }

    .guideline-actions {
      display: flex;
      gap: 8px;
      justify-content: flex-start;
    }
  }
}

.pagination-container {
  display: flex;
  justify-content: center;
  padding: 20px 0;
}
</style>
