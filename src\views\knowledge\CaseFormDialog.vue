<template>
  <el-dialog
    v-model="visible"
    :title="isEdit ? '编辑案例' : '新增案例'"
    width="800px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      label-width="120px"
      label-position="left"
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="案例名称" prop="info.caseName">
            <el-input v-model="formData.info.caseName" placeholder="请输入案例名称" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="实施地区" prop="info.applicableRegion">
            <el-cascader
              v-model="formData.info.applicableRegion"
              placeholder="请选择适用地区"
              :options="regionOptions"
              filterable
              clearable
              :props="{
                multiple: true,
                value: 'code',
                label: 'name',
                children: 'children',
                // 父子节点不强制关联选中（多选更灵活）
                checkStrictly: true,
                emitPath: false,
              }"
              style="width: 100%"
            >
            </el-cascader>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="实施对象" prop="info.implementationTarget">
            <el-input v-model="formData.info.implementationTarget" placeholder="请输入实施目标" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="问题类型" prop="info.caseProblemTypes">
            <el-select
              v-model="formData.info.caseProblemTypes"
              placeholder="请选择问题类型"
              multiple
              style="width: 100%"
            >
              <el-option
                v-for="item in problemTypeOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-form-item label="实施期间" prop="info.implementationPeriod">
        <div
          v-for="(period, index) in formData.info.implementationPeriod"
          :key="index"
          class="period-item"
        >
          <el-date-picker
            v-model="period.startDate"
            type="date"
            placeholder="开始日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            style="width: 200px"
          />
          <span style="margin: 0 10px">至</span>
          <el-date-picker
            v-model="period.endDate"
            type="date"
            placeholder="结束日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            style="width: 200px"
          />
          <el-button
            v-if="formData.info.implementationPeriod.length > 1"
            type="danger"
            size="small"
            @click="removePeriod(index)"
            style="margin-left: 10px"
          >
            删除
          </el-button>
        </div>
        <el-button
          type="primary"
          size="small"
          @click="addPeriod"
          style="margin-left: 10px; margin-top: -10px"
        >
          添加期间
        </el-button>
      </el-form-item>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="应用对象" prop="info.applicationTarget">
            <el-input v-model="formData.info.applicationTarget" placeholder="请输入应用对象" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="核心主题" prop="info.coreTheme">
            <el-input v-model="formData.info.coreTheme" placeholder="请输入核心主题" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="特色内容" prop="info.featuredContent">
            <el-input v-model="formData.info.featuredContent" placeholder="请输入特色内容" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="案例来源" prop="info.caseSource">
            <el-input v-model="formData.info.caseSource" placeholder="请输入案例来源" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-form-item label="关联政策" prop="info.policyIds">
        <el-select
          v-model="formData.info.policyIds"
          placeholder="请选择关联政策"
          multiple
          style="width: 100%"
        >
          <el-option
            v-for="item in policyOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="备注" prop="info.remarks">
        <el-input
          v-model="formData.info.remarks"
          type="textarea"
          :rows="3"
          placeholder="请输入备注"
        />
      </el-form-item>

      <el-form-item label="附件" prop="fileList" required>
        <el-upload
          ref="uploadRef"
          :action="uploadAction"
          :headers="uploadHeaders"
          :data="uploadData"
          :file-list="fileList"
          :on-success="handleUploadSuccess"
          :on-error="handleUploadError"
          :on-remove="handleFileRemove"
          :before-upload="beforeUpload"
          multiple
          drag
          class="upload-area"
        >
          <el-icon class="el-icon--upload"><upload-filled /></el-icon>
          <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
          <template #tip>
            <div class="el-upload__tip">
              支持上传 PDF、DOC、DOCX、XLS、XLSX 等格式文件，单个文件不超过 10MB
            </div>
          </template>
        </el-upload>
      </el-form-item>
    </el-form>

    <template #footer>
      <div>
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSubmit(2)" :loading="loading"> 保存草稿 </el-button>
        <el-button type="primary" @click="handleSubmit(3)" :loading="loading">
          {{ isEdit ? '更新' : '保存' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, watch, computed } from 'vue'
import { ElMessage } from 'element-plus'
import type { FormInstance, FormRules, UploadFile } from 'element-plus'
import type { CaseInfo, CaseFormData } from '@/api/types'
import { caseApi } from '@/api/knowledge'
import { fetchDictDataByTypes } from '@/utils/options'
import { getDistrict } from '@/api/common'
import { config } from '@/config/env'
import { UploadFilled } from '@element-plus/icons-vue'

interface Props {
  modelValue: boolean
  caseData?: CaseInfo | null
  isEdit: boolean
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
  (e: 'success'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 组件引用
const formRef = ref<FormInstance>()
// 数据状态
const loading = ref(false)
// 下拉选项
const regionOptions = ref<any[]>([])
const problemTypeOptions = ref<any[]>([])
const policyOptions = ref<any[]>([])

// 表单数据
const formData = reactive<any>({
  info: {
    caseName: '',
    applicableRegion: [],
    implementationTarget: '',
    implementationPeriod: [{ startDate: '', endDate: '' }],
    caseProblemTypes: [],
    applicationTarget: '',
    coreTheme: '',
    featuredContent: '',
    caseSource: '',
    policyIds: [],
    dataStatus: 1,
    remarks: '',
  },
  fileList: {
    addFileIds: [] as string[],
    delFileIds: [] as string[],
  },
})
// 表单验证规则
const rules: FormRules = {
  'info.caseName': [{ required: true, message: '请输入案例名称', trigger: 'blur' }],
  'info.applicableRegion': [{ required: true, message: '请选择适用地区', trigger: 'change' }],
  'info.caseProblemTypes': [{ required: true, message: '请选择问题类型', trigger: 'change' }],
  'info.applicationTarget': [{ required: true, message: '请输入应用对象', trigger: 'blur' }],
  'info.dataStatus': [{ required: true, message: '请选择数据状态', trigger: ['blur', 'change'] }],
  'fileList.addFileIds': [
    {
      required: true,
      message: '请上传附件',
      trigger: 'change',
      validator: (rule: any, value: string[], callback: Function) => {
        if (formData.fileList.addFileIds.length === 0) {
          callback(new Error('请至少上传一个附件'))
        } else {
          callback()
        }
      },
    },
  ],
}

// 计算属性
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value),
})
const fileList = ref<any[]>([])
// 监听案例数据变化
watch(
  () => props.modelValue,
  (newVal) => {
    if (!newVal) {
      resetForm()
    }
  },
)
watch(
  () => props.caseData,
  (newData) => {
    if (newData && props.isEdit) {
      Object.assign(formData, {
        info: {
          ...newData,
          implementationPeriod: newData.implementationPeriod?.length
            ? newData.implementationPeriod
            : [{ startDate: '', endDate: '' }],
        },
        fileList: {
          addFileIds: newData.fileList?.map((file: any) => file.fileId) || [],
          delFileIds: [],
        },
      })

      // 处理文件列表
      if (newData.fileList) {
        fileList.value = newData.fileList.map((file: any) => ({
          name: file.fileName,
          url: file.url,
          uid: file.fileId,
        }))
      }
    } else {
      // resetForm()
    }
  },
  { immediate: true },
)

// 方法
const resetForm = () => {
  Object.assign(formData, {
    info: {
      caseName: '',
      applicableRegion: [],
      implementationTarget: '',
      implementationPeriod: [{ startDate: '', endDate: '' }],
      caseProblemTypes: [],
      applicationTarget: '',
      coreTheme: '',
      featuredContent: '',
      caseSource: '',
      policyIds: [],
      dataStatus: 1,
      remarks: '',
    },
    fileList: {
      addFileIds: [] as string[],
      delFileIds: [] as string[],
    },
  })
  fileList.value = []
  formRef.value?.clearValidate()
}

const addPeriod = () => {
  formData.info.implementationPeriod.push({ startDate: '', endDate: '' })
}

const removePeriod = (index: number) => {
  formData.info.implementationPeriod.splice(index, 1)
}

// 文件上传配置

const uploadAction = config.baseURL + '/File/upload'
const uploadHeaders = {
  Authorization: 'Bearer ' + (localStorage.getItem('token') || ''),
}
const uploadData = {
  type: 'caseInfo',
}
// 文件上传相关方法
const beforeUpload = (file: File) => {
  const allowedTypes = [
    'application/pdf',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'application/vnd.ms-excel',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
  ]

  const isAllowedType = allowedTypes.includes(file.type)
  const isLt10M = file.size / 1024 / 1024 < 10

  if (!isAllowedType) {
    ElMessage.error('只能上传 PDF、DOC、DOCX、XLS、XLSX 格式的文件!')
    return false
  }
  if (!isLt10M) {
    ElMessage.error('上传文件大小不能超过 10MB!')
    return false
  }
  return true
}

const handleUploadSuccess = (response: any, file: UploadFile, fileList: any) => {
  if (response.code === 200) {
    formData.fileList.addFileIds.push(response.data.fileId)
    file.response = response.data
    ElMessage.success('文件上传成功')
    formRef.value?.validateField('fileList.addFileIds')
  } else {
    ElMessage.error(response.message || '文件上传失败')
    const index = fileList.findIndex((f: any) => f.uid === file.uid)
    if (index > -1) {
      fileList.splice(index, 1)
    }
  }
}

const handleUploadError = (error: any) => {
  ElMessage.error('文件上传失败: ' + error.message)
}

const handleFileRemove = (file: UploadFile) => {
  console.log('%c Line:483 🥚 file', 'color:#2eafb0', file)
  // 过滤文件里面对应的uid相同的数据

  formData.fileList.delFileIds.push(String(file.uid))
  formData.fileList.addFileIds = formData.fileList.addFileIds.filter(
    (id: string) => id !== String(file.uid),
  )
  fileList.value = fileList.value.filter((f) => f.uid !== file.uid)
  // 如果文件有response数据，说明是上传成功的文件
  // const response = file.response as any
  // if (response && response.fileId) {
  //   const index = formData.fileList.addFileIds.findIndex((id) => id === response.fileId)
  //   if (index > -1) {
  //     formData.fileList.addFileIds.splice(index, 1)
  //   }
  // }
  // 触发表单验证
  formRef.value?.validateField('fileList.addFileIds')
}
const handleSubmit = async (status: number) => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()
    loading.value = true
    const submitData = {
      ...formData,
      info: {
        ...formData.info,
        dataStatus: status, // 暂存状态
      },
    }
    delete submitData.info.fileList
    // const submitData: CaseFormData = {
    //   info: { ...formData },
    //   fileList: {
    //     addFileIds: fileList.value.map((file) => file.uid as string).filter(Boolean),
    //   },
    // }

    const api = props.isEdit ? caseApi.updateCaseInfo : caseApi.saveCaseInfo
    await api(submitData)
    ElMessage.success(props.isEdit ? '更新成功' : '保存成功')
    emit('success')
    handleClose()
  } catch (error) {
    console.error('提交失败:', error)
    ElMessage.error('操作失败')
  } finally {
    loading.value = false
  }
}

const handleClose = () => {
  visible.value = false
  resetForm()
}
onMounted(() => {
  initOptions()
})
// 初始化选项数据
const initOptions = async () => {
  const dictTypeMap = {
    problemTypeOptions: 'alwtlx',
  }
  // 获取字典数据
  const dictData = await fetchDictDataByTypes(dictTypeMap)
  problemTypeOptions.value = dictData.problemTypeOptions
  try {
    const res = await getDistrict({ code: '' })
    regionOptions.value = res
    // 获取政策选项
    const policyRes = await caseApi.getPolicyDictList()
    policyOptions.value = policyRes
  } catch (error) {
    console.error('获取选项数据失败:', error)
  }
}
</script>

<style scoped>
.period-item {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.dialog-footer {
  text-align: right;
}
</style>
