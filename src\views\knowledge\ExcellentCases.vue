<template>
  <div class="policy-info-list">
    <CollapsePanel
      title="优秀案例库查询"
      :show-toggle-btn="false"
      :expended="true"
      default-max-height="288px"
    >
      <template #panel-button>
        <el-button type="primary" @click="handleSearch" size="small">
          <el-icon><Search /></el-icon>
          搜索
        </el-button>
        <el-button @click="handleReset" size="small">重置</el-button>
      </template>
      <template #panel-main>
        <!-- 搜索表单 -->
        <SearchForm
          ref="searchFormRef"
          v-model="searchParams"
          :config="searchConfig"
          :first-row-count="4"
        />
      </template>
    </CollapsePanel>
    <CollapsePanel
      :title="`共找到${total}条记录`"
      :show-toggle-btn="false"
      :expended="true"
      default-max-height="288px"
    >
      <template #panel-button>
        <el-button type="primary" @click="handleAdd" size="small">
          <el-icon><Plus /></el-icon>
          新增案例
        </el-button>
      </template>
      <template #panel-main>
        <!-- 列表 -->
        <div class="policy-list">
          <div v-if="loading" class="loading-container">
            <el-skeleton :rows="3" animated />
          </div>
          <div v-else-if="caseList.length === 0" class="empty-container">
            <el-empty description="暂无数据" />
          </div>
          <div v-else>
            <div class="list">
              <!-- 案例列表 -->
              <el-table v-loading="loading" :data="caseList" style="width: 100%">
                <!-- <el-table-column type="selection" width="55" /> -->
                <el-table-column prop="caseName" label="案例名称" />
                <el-table-column prop="applicableRegionName" label="适用地区" />
                <el-table-column prop="caseProblemTypesName" label="问题类型" />
                <el-table-column prop="implementationPeriodName" label="实施期间">
                  <template #default="{ row }">
                    <el-tooltip :content="row.implementationPeriodName">
                      <el-text class="w-150px mb-2" truncated>
                        {{ row.implementationPeriodName }}
                      </el-text>
                    </el-tooltip>
                  </template>
                </el-table-column>
                <el-table-column prop="policyIdsName" label="关联政策">
                  <template #default="{ row }">
                    <el-tooltip :content="row.policyIdsName">
                      <el-text truncated>
                        {{ row.policyIdsName }}
                      </el-text>
                    </el-tooltip>
                  </template>
                </el-table-column>
                <el-table-column prop="dataStatusName" label="状态" />
                <el-table-column prop="updateTime" label="更新时间" />
                <el-table-column label="操作" fixed="right" width="300">
                  <template #default="{ row }">
                    <el-button size="small" @click="handleView(row)">查看</el-button>
                    <el-button size="small" type="primary" @click="handleEdit(row)">编辑</el-button>
                    <el-button
                      v-if="row.dataStatus === 3"
                      size="small"
                      type="primary"
                      @click="approvedCase(row)"
                      >审批</el-button
                    >
                    <el-button size="small" type="danger" @click="handleDelete(row)"
                      >删除</el-button
                    >
                  </template>
                </el-table-column>
              </el-table>

              <div class="pagination-container" v-if="total > 0">
                <el-pagination
                  v-model:current-page="pagination.currentPage"
                  v-model:page-size="pagination.pageSize"
                  :page-sizes="[10, 20, 50, 100]"
                  :total="total"
                  layout="total, sizes, prev, pager, next, jumper"
                  @size-change="handleSizeChange"
                  @current-change="handleCurrentChange"
                />
              </div>
            </div>
          </div>
        </div>
      </template>
    </CollapsePanel>

    <!-- 案例表单对话框 -->
    <CaseFormDialog
      v-model="dialogVisible"
      :case-data="currentCase"
      :is-edit="isEdit"
      width="1000px"
      @success="handleFormSuccess"
    />

    <!-- 案例详情对话框 -->
    <CaseDetailDialog v-model="detailVisible" :case-data="currentCase" />

    <!-- 审批弹窗 -->
    <el-dialog v-model="approvedVisible" title="审批意见" width="500">
      <div>
        <el-radio-group v-model="approvedStatus">
          <el-radio label="1">通过</el-radio>
          <el-radio label="-3">驳回</el-radio>
        </el-radio-group>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="approvedVisible = false">取消</el-button>
          <el-button type="primary" @click="submitApproved"> 提交 </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Search, Plus } from '@element-plus/icons-vue'
import CollapsePanel from '@/components/CollapsePanel.vue'
import SearchForm from '@/components/SearchForm.vue'
import type { FormItem } from '@/components/SearchForm.vue'
import type { CaseInfo } from '@/api/types'
import { caseApi } from '@/api/knowledge'
import { fetchDictDataByTypes } from '@/utils/options'
import { getDistrict } from '@/api/common'
import CaseFormDialog from '@/views/knowledge/CaseFormDialog.vue'
import CaseDetailDialog from '@/views/knowledge/CaseDetailDialog.vue'
// 字典查询
const initDictData = async () => {
  const dictTypeMap = {
    caseProblemTypes: 'alwtlx',
  }
  // 获取字典数据
  const dictData = await fetchDictDataByTypes(dictTypeMap)
  searchConfig.value.forEach((item) => {
    if (item.type === 'select' && dictData[item.prop]) {
      if (!item.attrs) item.attrs = {}
      item.attrs.options = dictData[item.prop]
    }
  })
  Object.assign(dictOptions, dictData)
}
const dictOptions = reactive<{ [key: string]: any[] }>({
  policyLevel: [] as any[],
  policyType: [] as any[],
  controlledPollutants: [] as any[],
})
// 地区
const applicableRegionOptions = ref([] as any[])
const fetchApplicableRegion = async () => {
  try {
    const res = await getDistrict({ code: '' })
    applicableRegionOptions.value = res
  } catch (error) {
    console.error('获取地区数据失败:', error)
  }
}
fetchApplicableRegion()

// 状态
const dataStatusOptions = ref([] as any[])
const fetchDataStatus = async () => {
  try {
    const res = await caseApi.getDataStatusList()
    dataStatusOptions.value = res
  } catch (error) {
    console.error('获取状态数据失败:', error)
  }
}
fetchDataStatus()
// 搜索参数
const searchParams = ref({
  caseName: '',
  applicableRegion: '',
  caseProblemTypes: '',
  updateStartDate: '',
  updateDate: [],
  updateEndDate: '',
  dataStatus: undefined,
})

// 搜索表单配置
const searchConfig = ref<FormItem[]>([
  {
    type: 'input',
    prop: 'caseName',
    formItem: { label: '案例名称' },
    attrs: {
      placeholder: '请输入案例名称',
      clearable: true,
    },
  },
  {
    type: 'cascader',
    prop: 'applicableRegion',
    formItem: { label: '适用地区' },
    attrs: {
      placeholder: '请输入适用地区',
      options: applicableRegionOptions,
      filterable: true,
      clearable: true,
      props: {
        value: 'code',
        label: 'name',
        children: 'children',
        checkStrictly: true,
        emitPath: false,
      },
    },
  },
  {
    type: 'select',
    prop: 'caseProblemTypes',
    formItem: { label: '案例问题类型' },
    attrs: {
      placeholder: '请选择问题类型',
      clearable: true,
      options: [],
    },
  },
  {
    type: 'daterange',
    prop: 'updateDate',
    formItem: { label: '更新时间' },
    attrs: {
      clearable: true,
      format: 'YYYY-MM-DD HH:mm:ss',
      valueFormat: 'YYYY-MM-DD HH:mm:ss',
    },
  },
  {
    type: 'select',
    prop: 'dataStatus',
    formItem: { label: '数据状态' },
    attrs: {
      placeholder: '请选择数据状态',
      clearable: true,
      options: dataStatusOptions,
    },
  },
])

// 数据状态
const loading = ref(false)
const total = ref(0)
const caseList = ref<any[]>([])
const searchFormRef = ref()
const pagination = reactive({
  currentPage: 1,
  pageSize: 10,
})

// 弹窗相关状态
const dialogVisible = ref(false)
const isEdit = ref(false)
const editData = ref<any>(null)
const detailVisible = ref(false)
const currentCase = ref<any>(null)
// 审批相关
const approvedVisible = ref(false)
const approvedStatus = ref('')
const approvedCaseId = ref('')
const approvedCase = (row: any) => {
  approvedCaseId.value = row.caseId
  approvedVisible.value = true
}
const submitApproved = () => {
  if (!approvedStatus.value) {
    return ElMessage.error('请选择审批意见')
  }
  approvedVisible.value = false
  caseApi
    .approvedCaseInfo({
      info: {
        caseId: approvedCaseId.value,
        dataStatus: approvedStatus.value,
      },
    })
    .then(() => {
      ElMessage.success('审批成功')
      getCaseList()
    })
    .catch((error) => {
      console.log('%c Line:462 🌮 error', 'color:#2eafb0', error)
      ElMessage.error('审批失败')
    })
}
const handleFormSuccess = () => {
  getCaseList()
}
// 获取政策类型样式类
const getPolicyTypeClass = (type: string) => {
  const typeMap: Record<string, string> = {
    1: 'law-regulation',
    2: 'standard',
    3: 'notice',
    4: 'guide',
  }
  return typeMap[type] || 'default'
}

// 搜索处理
const handleSearch = () => {
  pagination.currentPage = 1
  getCaseList()
}
const handleReset = () => {
  Object.assign(searchParams.value, {
    ...pagination,
    caseName: '',
    applicableRegion: '',
    caseProblemTypes: '',
    updateStartDate: '',
    updateEndDate: '',
    dataStatus: undefined,
    updateDate: [],
  })
  getCaseList()
}
const handleSizeChange = (size: number) => {
  pagination.pageSize = size
  pagination.currentPage = 1
  getCaseList()
}
const handleCurrentChange = (page: number) => {
  pagination.currentPage = page
  getCaseList()
}

// 增删改查
const handleAdd = () => {
  currentCase.value = null
  isEdit.value = false
  dialogVisible.value = true
}
const handleEdit = (row?: CaseInfo) => {
  currentCase.value = row
  isEdit.value = true
  dialogVisible.value = true
}
const handleView = (row: CaseInfo) => {
  currentCase.value = row
  detailVisible.value = true
}
const handleDelete = async (row?: CaseInfo) => {
  const caseToDelete = row
  if (!caseToDelete) return

  try {
    await ElMessageBox.confirm(`确定要删除案例"${caseToDelete.caseName}"吗？`, '确认删除', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })

    const res = await caseApi.deleteCaseInfo({
      info: { caseId: caseToDelete.caseId! },
    })

    ElMessage.success('删除成功')
    getCaseList()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除案例失败:', error)
      ElMessage.error('删除失败')
    }
  }
}

// 获取政策列表数据
const getCaseList = async () => {
  loading.value = true
  try {
    searchParams.value.updateStartDate = searchParams.value.updateDate[0]
    searchParams.value.updateEndDate = searchParams.value.updateDate[1]
    const params = {
      ...searchParams.value,
      // controlledPollutants: Array.isArray(searchParams.value.controlledPollutants)
      //   ? searchParams.value.controlledPollutants.join(',')
      //   : searchParams.value.controlledPollutants,
      ...pagination,
    }
    const res = await caseApi.getCaseInfoList(params)
    caseList.value = res.list || []
    total.value = res.pagination.total || 0
  } catch (error: any) {
    console.error('获取列表失败:', error)
    caseList.value = []
    total.value = 0
  } finally {
    loading.value = false
  }
}
// 查找字典表中对应value的label
const getDictLabel = (type: string, value: string) => {
  const dict = dictOptions[type].find((item: any) => item.value === value)
  return dict ? dict.label : value
}
// 组件挂载时获取数据
onMounted(() => {
  getCaseList()
  initDictData()
})
</script>

<style scoped>
.policy-info-list {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: 100vh;
}

.page-header {
  text-align: center;
  margin-bottom: 30px;
  padding: 40px 0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 12px;
}

.page-header h1 {
  font-size: 32px;
  font-weight: 600;
  margin: 0 0 10px 0;
}

.page-header p {
  font-size: 16px;
  opacity: 0.9;
  margin: 0;
}

.search-card {
  margin-bottom: 20px;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.search-actions {
  display: flex;
  justify-content: center;
  gap: 12px;
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid #ebeef5;
}

.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 16px 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.toolbar-left .result-count {
  color: #606266;
  font-size: 14px;
}

.toolbar-right {
  display: flex;
  gap: 12px;
  align-items: center;
}

.policy-list {
  background: white;
  border-radius: 12px;
  overflow: hidden;
  /* box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1); */
}

.loading-container,
.empty-container {
  padding: 40px;
  text-align: center;
}

.policy-item {
  padding: 24px;
  margin: 24px;
  border: 1px solid #eee;
  border-radius: 16px;
  transition: all 0.3s ease;
}

.policy-item:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.policy-header {
  display: flex;
  align-items: flex-start;
  gap: 16px;
  margin-bottom: 16px;
}

.policy-type-tag {
  padding: 4px 12px;
  border-radius: 16px;
  font-size: 12px;
  font-weight: 500;
  white-space: nowrap;
  flex-shrink: 0;
}

.policy-type-tag.law-regulation {
  background-color: #e7f3ff;
  color: #1890ff;
}

.policy-type-tag.standard {
  background-color: #f6ffed;
  color: #52c41a;
}

.policy-type-tag.notice {
  background-color: #fff7e6;
  color: #fa8c16;
}

.policy-type-tag.guide {
  background-color: #f9f0ff;
  color: #722ed1;
}

.policy-type-tag.default {
  background-color: #f5f5f5;
  color: #666;
}

.policy-title {
  flex: 1;
  min-width: 0;
}

.policy-title h3 {
  margin: 0 0 8px 0;
  font-size: 18px;
  font-weight: 600;
  color: #262626;
  line-height: 1.4;
  cursor: pointer;
  transition: color 0.3s ease;
}

.policy-title h3:hover {
  color: #1890ff;
}

.policy-number {
  font-size: 14px;
  color: #8c8c8c;
}

.policy-actions {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
  align-items: center;
}

.policy-actions .el-button {
  padding: 4px 8px;
  font-size: 12px;
  border: none;
}

.policy-actions .delete-btn {
  color: #ff4d4f;
}

.policy-actions .delete-btn:hover {
  color: #ff7875;
  background-color: #fff2f0;
}

.policy-meta {
  display: flex;
  gap: 24px;
  margin-bottom: 12px;
  flex-wrap: wrap;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 14px;
  color: #666;
}

.meta-item .el-icon {
  font-size: 16px;
  color: #999;
}

.policy-content {
  font-size: 14px;
  color: #595959;
  line-height: 1.6;
  margin-top: 12px;
  padding: 12px;
  background-color: #fafafa;
  border-radius: 6px;
  border-left: 3px solid #1890ff;
}

.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 32px;
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .policy-info-list {
    padding: 12px;
  }

  .page-header {
    padding: 20px 0;
  }

  .page-header h1 {
    font-size: 24px;
  }

  .toolbar {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }

  .toolbar-right {
    justify-content: center;
  }

  .policy-header {
    flex-direction: column;
    gap: 12px;
  }

  .policy-actions {
    justify-content: center;
  }

  .policy-meta {
    flex-direction: column;
    gap: 8px;
  }
}
</style>
