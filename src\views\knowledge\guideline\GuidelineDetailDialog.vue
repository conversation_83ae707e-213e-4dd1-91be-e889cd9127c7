<template>
  <el-dialog
    v-model="dialogVisible"
    title="核算系数库详情"
    width="900px"
    :close-on-click-modal="false"
  >
    <div v-if="guidelineData" class="detail-container">
      <!-- 基本信息 -->
      <div class="detail-section">
        <h3 class="section-title">基本信息</h3>
        <el-row :gutter="20">
          <el-col :span="12">
            <div class="detail-item">
              <label class="detail-label">核算指南名称：</label>
              <span class="detail-value">{{ guidelineData.guidelineName }}</span>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="detail-item">
              <label class="detail-label">发布部门：</label>
              <span class="detail-value">{{ guidelineData.issuingDepartment }}</span>
            </div>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <div class="detail-item">
              <label class="detail-label">发布日期：</label>
              <span class="detail-value">{{ guidelineData.issueDate }}</span>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="detail-item">
              <label class="detail-label">用途：</label>
              <span class="detail-value">{{ guidelineData.purposeName }}</span>
            </div>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <div class="detail-item">
              <label class="detail-label">数据状态：</label>
              <el-tag :type="getStatusTagType(guidelineData.dataStatus)">
                {{ guidelineData.dataStatusName }}
              </el-tag>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="detail-item">
              <label class="detail-label">更新时间：</label>
              <span class="detail-value">{{
                guidelineData.updateTime || guidelineData.inputTime
              }}</span>
            </div>
          </el-col>
        </el-row>
      </div>

      <!-- 详细信息 -->
      <div class="detail-section">
        <h3 class="section-title">详细信息</h3>

        <div class="detail-item">
          <label class="detail-label">适用范围：</label>
          <div class="detail-value detail-text">{{ guidelineData.applicableScope }}</div>
        </div>

        <div class="detail-item">
          <label class="detail-label">核算对象说明：</label>
          <div class="detail-value detail-text">{{ guidelineData.accountingEntity }}</div>
        </div>

        <div class="detail-item">
          <label class="detail-label">核算方法：</label>
          <div class="detail-value detail-text">{{ guidelineData.methodology }}</div>
        </div>

        <div class="detail-item">
          <label class="detail-label">关键公式：</label>
          <div class="detail-value detail-text">{{ guidelineData.keyFormulas }}</div>
        </div>

        <div v-if="guidelineData.remarks" class="detail-item">
          <label class="detail-label">备注：</label>
          <div class="detail-value detail-text">{{ guidelineData.remarks }}</div>
        </div>
      </div>

      <!-- 附件信息 -->
      <div
        v-if="guidelineData.fileList && guidelineData.fileList.length > 0"
        class="detail-section"
      >
        <h3 class="section-title">附件信息</h3>
        <div class="file-list">
          <div v-for="file in guidelineData.fileList" :key="file.fileId" class="file-item">
            <div class="file-info">
              <el-icon class="file-icon"><Document /></el-icon>
              <span class="file-name">{{ file.fileName }}</span>
            </div>
            <div class="file-actions">
              <el-button type="primary" size="small" @click="handlePreview(file)">
                <el-icon><View /></el-icon>
                预览
              </el-button>
              <el-button size="small" @click="handleDownload(file)">
                <el-icon><Download /></el-icon>
                下载
              </el-button>
            </div>
          </div>
        </div>
      </div>

      <!-- 操作记录 -->
      <div class="detail-section">
        <h3 class="section-title">操作记录</h3>
        <el-row :gutter="20">
          <el-col :span="12">
            <div class="detail-item">
              <label class="detail-label">创建人：</label>
              <span class="detail-value">{{ guidelineData.inputUser || '系统' }}</span>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="detail-item">
              <label class="detail-label">创建时间：</label>
              <span class="detail-value">{{ guidelineData.inputTime }}</span>
            </div>
          </el-col>
        </el-row>

        <el-row v-if="guidelineData.updateUser || guidelineData.updateTime" :gutter="20">
          <el-col :span="12">
            <div class="detail-item">
              <label class="detail-label">更新人：</label>
              <span class="detail-value">{{ guidelineData.updateUser || '系统' }}</span>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="detail-item">
              <label class="detail-label">更新时间：</label>
              <span class="detail-value">{{ guidelineData.updateTime }}</span>
            </div>
          </el-col>
        </el-row>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="dialogVisible = false">关闭</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { Document, View, Download } from '@element-plus/icons-vue'
import type { GuidelineInfo, GuidelineFile } from '@/api/types'
import { previewFile, downloadFile, type FileInfo } from '@/utils/fileUtils'

// Props
interface Props {
  visible: boolean
  guidelineData?: GuidelineInfo | null
}

const props = withDefaults(defineProps<Props>(), {
  guidelineData: null,
})

// Emits
const emit = defineEmits<{
  'update:visible': [value: boolean]
}>()

// 计算属性
const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value),
})

// 获取状态标签类型
const getStatusTagType = (status: number) => {
  switch (status) {
    case 1:
      return 'success' // 正常
    case 2:
      return 'info' // 暂存
    case 3:
      return 'warning' // 待审核
    case -1:
      return 'danger' // 失效
    default:
      return 'info'
  }
}

// 预览文件
const handlePreview = (file: GuidelineFile) => {
  const fileInfo: FileInfo = {
    fileId: file.fileId,
    fileName: file.fileName,
    url: file.url,
    filePath: file.filePath,
    fileType: file.fileType,
  }
  previewFile(fileInfo)
}

// 下载文件
const handleDownload = (file: GuidelineFile) => {
  const fileInfo: FileInfo = {
    fileId: file.fileId,
    fileName: file.fileName,
    url: file.url,
    filePath: file.filePath,
    fileType: file.fileType,
  }
  downloadFile(fileInfo)
}
</script>

<style scoped lang="scss">
.detail-container {
  max-height: 70vh;
  overflow-y: auto;
}

.detail-section {
  margin-bottom: 24px;

  &:last-child {
    margin-bottom: 0;
  }
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  margin: 0 0 16px 0;
  padding-bottom: 8px;
  border-bottom: 1px solid #e4e7ed;
}

.detail-item {
  margin-bottom: 16px;

  &:last-child {
    margin-bottom: 0;
  }
}

.detail-label {
  display: inline-block;
  width: 120px;
  font-weight: 500;
  color: #606266;
  margin-bottom: 4px;
}

.detail-value {
  color: #303133;

  &.detail-text {
    display: block;
    line-height: 1.6;
    white-space: pre-wrap;
    word-break: break-word;
    background-color: #f5f7fa;
    padding: 12px;
    border-radius: 4px;
    margin-top: 4px;
  }
}

.file-list {
  .file-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px;
    border: 1px solid #e4e7ed;
    border-radius: 4px;
    margin-bottom: 8px;

    &:last-child {
      margin-bottom: 0;
    }

    .file-info {
      display: flex;
      align-items: center;
      flex: 1;

      .file-icon {
        font-size: 20px;
        color: #409eff;
        margin-right: 8px;
      }

      .file-name {
        color: #303133;
        font-weight: 500;
      }
    }

    .file-actions {
      display: flex;
      gap: 8px;
    }
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
}
</style>
