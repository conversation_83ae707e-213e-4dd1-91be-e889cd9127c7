<template>
  <div class="policy-info-list">
    <!-- 页面标题 -->
    <!-- <div class="page-header">
      <h1>政策文件库</h1>
      <p>汇集全国大气污染防治相关政策法规和标准规范</p>
    </div> -->
    <CollapsePanel
      title="政策库搜索"
      :show-toggle-btn="false"
      :expended="true"
      default-max-height="288px"
    >
      <template #panel-button>
        <el-button type="primary" @click="handleSearch" size="small">
          <el-icon><Search /></el-icon>
          搜索
        </el-button>
        <el-button @click="handleReset" size="small">重置</el-button>
      </template>
      <template #panel-main>
        <!-- 搜索表单 -->
        <SearchForm
          ref="searchFormRef"
          v-model="searchParams"
          :config="searchConfig"
          :first-row-count="4"
        />
      </template>
    </CollapsePanel>
    <CollapsePanel
      :title="`共找到${total}条记录`"
      :show-toggle-btn="false"
      :expended="true"
      default-max-height="288px"
    >
      <template #panel-button>
        <el-button type="primary" @click="handleAdd" size="small">
          <el-icon><Plus /></el-icon>
          新增政策
        </el-button>
        <!-- <el-dropdown @command="handleSortChange" size="small">
          <el-button>
            排序方式：{{ sortOptions.find((item) => item.value === currentSort)?.label }}
            <el-icon class="el-icon--right"><ArrowDown /></el-icon>
          </el-button>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item
                v-for="option in sortOptions"
                :key="option.value"
                :command="option.value"
              >
                {{ option.label }}
              </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown> -->
      </template>
      <template #panel-main>
        <!-- 政策列表 -->
        <div class="policy-list">
          <div v-if="loading" class="loading-container">
            <el-skeleton :rows="3" animated />
          </div>
          <div v-else-if="policyList.length === 0" class="empty-container">
            <el-empty description="暂无政策数据" />
          </div>
          <div v-else>
            <div v-for="policy in policyList" :key="policy.id" class="policy-item">
              <div class="policy-header">
                <div class="policy-type-tag" :class="getPolicyTypeClass(policy.policyType)">
                  {{ policy.policyTypeName || '政策文件' }}
                </div>
                <div class="policy-title">
                  <h3>{{ policy.policyName }}</h3>
                  <div class="policy-number" v-if="policy.policyNumber">
                    政策编号：{{ policy.policyNumber }}
                  </div>
                </div>
                <div class="policy-actions">
                  <el-button type="text" @click="handleView(policy)">
                    <el-icon><View /></el-icon>
                    查看
                  </el-button>
                  <el-button
                    type="text"
                    v-if="policy.dataStatus === 3"
                    @click="approvedPolicy(policy)"
                  >
                    <el-icon><View /></el-icon>
                    审批
                  </el-button>
                  <el-button type="text" @click="handleEdit(policy)">
                    <el-icon><Edit /></el-icon>
                    编辑
                  </el-button>
                  <!-- <el-button type="text" @click="handleDownload(policy)">
                    <el-icon><Download /></el-icon>
                    下载
                  </el-button>
                  <el-button type="text" @click="handleCollect(policy)">
                    <el-icon><Star /></el-icon>
                    {{ policy.isCollected ? '取消收藏' : '收藏' }}
                  </el-button>
                  <el-button type="text" @click="handleShare(policy)">
                    <el-icon><Share /></el-icon>
                    分享
                  </el-button> -->
                  <el-popconfirm title="确定要删除这个政策文件吗？" @confirm="handleDelete(policy)">
                    <template #reference>
                      <el-button type="text" class="delete-btn">
                        <el-icon><Delete /></el-icon>
                        删除
                      </el-button>
                    </template>
                  </el-popconfirm>
                </div>
              </div>

              <div class="policy-meta">
                <div class="meta-item">
                  <el-icon><Calendar /></el-icon>
                  <span>{{ formatDate(policy.publishDate) }}</span>
                </div>
                <div class="meta-item" v-if="policy.policyLevel">
                  <el-icon><Flag /></el-icon>
                  <span>{{ getDictLabel('policyLevel', policy.policyLevel) }}</span>
                </div>
                <div class="meta-item" v-if="policy.applicableRegion">
                  <el-icon><Location /></el-icon>
                  <span>{{ policy.applicableRegionName }}</span>
                </div>
                <div class="meta-item" v-if="policy.controlledPollutants">
                  <el-icon><Warning /></el-icon>
                  <span>{{ policy.controlledPollutantsName }}</span>
                </div>
              </div>

              <div class="policy-content" v-if="policy.description">
                {{ policy.description }}
              </div>
            </div>
          </div>
        </div>

        <!-- 分页 -->
        <div class="pagination-container" v-if="total > 0">
          <el-pagination
            v-model:current-page="pagination.currentPage"
            v-model:page-size="pagination.pageSize"
            :page-sizes="[10, 20, 50, 100]"
            :total="total"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </template>
    </CollapsePanel>

    <!-- 新增/编辑/查看政策弹窗 -->
    <PolicyFormDialog
      v-model:visible="dialogVisible"
      :is-edit="isEdit"
      :is-disabled="isDisabled"
      :edit-data="editData"
      @success="handleDialogSuccess"
    />

    <!-- 审批弹窗 -->
    <el-dialog v-model="approvedVisible" title="审批意见" width="500">
      <div>
        <el-radio-group v-model="approvedStatus">
          <el-radio label="1">通过</el-radio>
          <el-radio label="-3">驳回</el-radio>
        </el-radio-group>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="approvedVisible = false">取消</el-button>
          <el-button type="primary" @click="submitApproved"> 提交 </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import {
  Search,
  Plus,
  ArrowDown,
  View,
  Edit,
  Download,
  Star,
  Share,
  Delete,
  Calendar,
  Flag,
  Location,
  Warning,
} from '@element-plus/icons-vue'
import CollapsePanel from '@/components/CollapsePanel.vue'
import SearchForm from '@/components/SearchForm.vue'
import type { FormItem } from '@/components/SearchForm.vue'
import PolicyFormDialog from '@/views/knowledge/PolicyFormDialog.vue'
import { policyApi } from '@/api/knowledge'
import { fetchDictDataByTypes } from '@/utils/options'
import { getDistrict } from '@/api/common'

// 字典查询
const initDictData = async () => {
  const dictTypeMap = {
    policyLevel: 'zccj',
    policyType: 'zclx',
    controlledPollutants: 'sjwrw',
  }
  // 获取字典数据
  const dictData = await fetchDictDataByTypes(dictTypeMap)
  searchConfig.value.forEach((item) => {
    if (item.type === 'select' && dictData[item.prop]) {
      if (!item.attrs) item.attrs = {}
      item.attrs.options = dictData[item.prop]
    }
  })
  Object.assign(dictOptions, dictData)
}
const dictOptions = reactive<{ [key: string]: any[] }>({
  policyLevel: [] as any[],
  policyType: [] as any[],
  controlledPollutants: [] as any[],
})
// 地区
const applicableRegionOptions = ref([] as any[])
const fetchApplicableRegion = async () => {
  try {
    const res = await getDistrict({ code: '' })
    applicableRegionOptions.value = res
  } catch (error) {
    console.error('获取地区数据失败:', error)
  }
}
fetchApplicableRegion()

// 状态
const dataStatusOptions = ref([] as any[])
const fetchDataStatus = async () => {
  try {
    const res = await policyApi.getDataStatusList()
    dataStatusOptions.value = res
  } catch (error) {
    console.error('获取状态数据失败:', error)
  }
}
fetchDataStatus()
// 搜索参数
const searchParams = ref({
  policyName: '',
  publishDate: [],
  publishStartDate: '',
  publishEndDate: '',
  policyLevel: '',
  applicableRegion: '',
  policyType: '',
  policyNumber: '',
  controlledPollutants: [],
  dataStatus: '',
})

// 搜索表单配置
const searchConfig = ref<FormItem[]>([
  {
    type: 'input',
    prop: 'policyName',
    formItem: { label: '政策名称' },
    attrs: { placeholder: '请输入政策名称', clearable: true },
  },
  {
    type: 'input',
    prop: 'policyNumber',
    formItem: { label: '政策编号' },
    attrs: { placeholder: '请输入政策编号', clearable: true },
  },
  {
    type: 'select',
    prop: 'policyType',
    formItem: { label: '政策类型' },
    attrs: {
      placeholder: '请选择政策类型',
      clearable: true,
      options: [],
    },
  },
  {
    type: 'select',
    prop: 'policyLevel',
    formItem: { label: '政策级别' },
    attrs: {
      placeholder: '请选择政策级别',
      options: [],
      clearable: true,
    },
  },
  {
    type: 'cascader',
    prop: 'applicableRegion',
    formItem: { label: '适用地区' },
    attrs: {
      placeholder: '请输入适用地区',
      options: applicableRegionOptions,
      filterable: true,
      clearable: true,
      // collapseTags: true,
      // collapseTagsTooltip: true,
      props: {
        // multiple: true,
        value: 'code',
        label: 'name',
        children: 'children',
        // 父子节点不强制关联选中（多选更灵活）
        checkStrictly: true,
        emitPath: false,
      },
    },
  },
  {
    type: 'select',
    prop: 'controlledPollutants',
    formItem: { label: '管控污染物' },
    attrs: { placeholder: '请输入管控污染物', multiple: true, clearable: true },
  },
  {
    type: 'daterange',
    prop: 'publishDate',
    formItem: { label: '发布时间' },
    attrs: {
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD',
      startPlaceholder: '开始日期',
      endPlaceholder: '结束日期',
      clearable: true,
    },
  },
  {
    type: 'select',
    prop: 'dataStatus',
    formItem: { label: '数据状态' },
    attrs: {
      placeholder: '请选择数据状态',
      options: dataStatusOptions,
      clearable: true,
    },
  },
])

// 数据状态
const loading = ref(false)
const total = ref(0)
const policyList = ref<any[]>([])
const searchFormRef = ref()
const pagination = reactive({
  currentPage: 1,
  pageSize: 10,
})
// 排序选项
const sortOptions = [
  { label: '发布时间 ↓', value: 'publishTime_desc' },
  { label: '发布时间 ↑', value: 'publishTime_asc' },
  { label: '文件类型 ↓', value: 'policyType_desc' },
  { label: '文件类型 ↑', value: 'policyType_asc' },
]
const currentSort = ref('publishTime_desc')

// 弹窗相关状态
const dialogVisible = ref(false)
const isEdit = ref(false)
const editData = ref<any>(null)

// 审批相关
const approvedVisible = ref(false)
const approvedStatus = ref('')
const approvedPolicyId = ref('')

const approvedPolicy = (policy: any) => {
  approvedPolicyId.value = policy.policyId
  approvedVisible.value = true
}
const submitApproved = () => {
  if (!approvedStatus.value) {
    return ElMessage.error('请选择审批意见')
  }
  approvedVisible.value = false
  policyApi
    .approvedPolicyInfo({
      info: {
        policyId: approvedPolicyId.value,
        dataStatus: approvedStatus.value,
      },
    })
    .then(() => {
      ElMessage.success('审批成功')
      fetchPolicyList()
    })
    .catch((error) => {
      console.log('%c Line:462 🌮 error', 'color:#2eafb0', error)
      ElMessage.error('审批失败')
    })
}
// 获取政策类型样式类
const getPolicyTypeClass = (type: string) => {
  const typeMap: Record<string, string> = {
    1: 'law-regulation',
    2: 'standard',
    3: 'notice',
    4: 'guide',
  }
  return typeMap[type] || 'default'
}

// 格式化日期
const formatDate = (date: string) => {
  if (!date) return ''
  return new Date(date).toLocaleDateString('zh-CN')
}

// 搜索处理
const handleSearch = () => {
  pagination.currentPage = 1
  fetchPolicyList()
}

// 重置搜索
const handleReset = () => {
  Object.assign(searchParams.value, {
    ...pagination,
    policyName: '',
    publishStartDate: '',
    publishEndDate: '',
    policyLevel: '',
    applicableRegion: '',
    policyType: '',
    policyNumber: '',
    controlledPollutants: '',
    dataStatus: '',
  })
  fetchPolicyList()
}

// 排序变更
const handleSortChange = (sortValue: string) => {
  currentSort.value = sortValue
  fetchPolicyList()
}

// 分页处理
const handleSizeChange = (size: number) => {
  pagination.pageSize = size
  pagination.currentPage = 1
  fetchPolicyList()
}

const handleCurrentChange = (page: number) => {
  pagination.currentPage = page
  fetchPolicyList()
}

// 新增政策
const handleAdd = () => {
  isEdit.value = false
  editData.value = null
  dialogVisible.value = true
}

// 编辑政策
const handleEdit = (policy: any) => {
  isEdit.value = true
  editData.value = { ...policy }
  dialogVisible.value = true
}
// 查看
const isDisabled = ref(false)
const handleView = (policy: any) => {
  isEdit.value = true
  isDisabled.value = true
  editData.value = { ...policy }
  dialogVisible.value = true
}

// 弹窗成功回调
const handleDialogSuccess = () => {
  fetchPolicyList()
}

// 下载文件
const handleDownload = (policy: any) => {
  ElMessage.success(`开始下载：${policy.policyName}`)
}

// 收藏/取消收藏
const handleCollect = (policy: any) => {
  policy.isCollected = !policy.isCollected
  ElMessage.success(policy.isCollected ? '收藏成功' : '取消收藏成功')
}

// 分享
const handleShare = (policy: any) => {
  ElMessage.info(`分享政策：${policy.policyName}`)
}

// 删除政策
const handleDelete = async (policy: any) => {
  try {
    await policyApi.deletePolicyInfo({ info: { policyId: policy.policyId } })
    ElMessage.success('删除成功')
    fetchPolicyList()
  } catch (error) {
    ElMessage.error('删除失败')
  }
}

// 获取政策列表数据
const fetchPolicyList = async () => {
  loading.value = true
  try {
    searchParams.value.publishStartDate = searchParams.value.publishDate[0]
    searchParams.value.publishEndDate = searchParams.value.publishDate[1]
    const params = {
      ...searchParams.value,
      controlledPollutants: Array.isArray(searchParams.value.controlledPollutants)
        ? searchParams.value.controlledPollutants.join(',')
        : searchParams.value.controlledPollutants,
      ...pagination,
    }
    const res = await policyApi.getPolicyInfoList(params)
    policyList.value = res.list || []
    total.value = res.pagination.total || 0
  } catch (error: any) {
    console.error('获取政策列表失败:', error)
    policyList.value = []
    total.value = 0
  } finally {
    loading.value = false
  }
}
// 查找字典表中对应value的label
const getDictLabel = (type: string, value: string) => {
  const dict = dictOptions[type].find((item: any) => item.value === value)
  return dict ? dict.label : value
}
// 组件挂载时获取数据
onMounted(() => {
  fetchPolicyList()
  initDictData()
})
</script>

<style scoped>
.policy-info-list {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: 100vh;
}

.page-header {
  text-align: center;
  margin-bottom: 30px;
  padding: 40px 0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 12px;
}

.page-header h1 {
  font-size: 32px;
  font-weight: 600;
  margin: 0 0 10px 0;
}

.page-header p {
  font-size: 16px;
  opacity: 0.9;
  margin: 0;
}

.search-card {
  margin-bottom: 20px;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.search-actions {
  display: flex;
  justify-content: center;
  gap: 12px;
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid #ebeef5;
}

.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 16px 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.toolbar-left .result-count {
  color: #606266;
  font-size: 14px;
}

.toolbar-right {
  display: flex;
  gap: 12px;
  align-items: center;
}

.policy-list {
  background: white;
  border-radius: 12px;
  overflow: hidden;
  /* box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1); */
}

.loading-container,
.empty-container {
  padding: 40px;
  text-align: center;
}

.policy-item {
  padding: 24px;
  margin: 24px;
  border: 1px solid #eee;
  border-radius: 16px;
  transition: all 0.3s ease;
}

.policy-item:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.policy-header {
  display: flex;
  align-items: flex-start;
  gap: 16px;
  margin-bottom: 16px;
}

.policy-type-tag {
  padding: 4px 12px;
  border-radius: 16px;
  font-size: 12px;
  font-weight: 500;
  white-space: nowrap;
  flex-shrink: 0;
}

.policy-type-tag.law-regulation {
  background-color: #e7f3ff;
  color: #1890ff;
}

.policy-type-tag.standard {
  background-color: #f6ffed;
  color: #52c41a;
}

.policy-type-tag.notice {
  background-color: #fff7e6;
  color: #fa8c16;
}

.policy-type-tag.guide {
  background-color: #f9f0ff;
  color: #722ed1;
}

.policy-type-tag.default {
  background-color: #f5f5f5;
  color: #666;
}

.policy-title {
  flex: 1;
  min-width: 0;
}

.policy-title h3 {
  margin: 0 0 8px 0;
  font-size: 18px;
  font-weight: 600;
  color: #262626;
  line-height: 1.4;
  cursor: pointer;
  transition: color 0.3s ease;
}

.policy-title h3:hover {
  color: #1890ff;
}

.policy-number {
  font-size: 14px;
  color: #8c8c8c;
}

.policy-actions {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
  align-items: center;
}

.policy-actions .el-button {
  padding: 4px 8px;
  font-size: 12px;
  border: none;
}

.policy-actions .delete-btn {
  color: #ff4d4f;
}

.policy-actions .delete-btn:hover {
  color: #ff7875;
  background-color: #fff2f0;
}

.policy-meta {
  display: flex;
  gap: 24px;
  margin-bottom: 12px;
  flex-wrap: wrap;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 14px;
  color: #666;
}

.meta-item .el-icon {
  font-size: 16px;
  color: #999;
}

.policy-content {
  font-size: 14px;
  color: #595959;
  line-height: 1.6;
  margin-top: 12px;
  padding: 12px;
  background-color: #fafafa;
  border-radius: 6px;
  border-left: 3px solid #1890ff;
}

.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 32px;
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .policy-info-list {
    padding: 12px;
  }

  .page-header {
    padding: 20px 0;
  }

  .page-header h1 {
    font-size: 24px;
  }

  .toolbar {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }

  .toolbar-right {
    justify-content: center;
  }

  .policy-header {
    flex-direction: column;
    gap: 12px;
  }

  .policy-actions {
    justify-content: center;
  }

  .policy-meta {
    flex-direction: column;
    gap: 8px;
  }
}
</style>
